#!/bin/bash

echo "=== Quick Build for Indoor Navigation System ==="

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -d "src" ]; then
    print_error "Please run this script from the workspace root directory (indoor_autonomous_vehicle/)"
    exit 1
fi

print_status "Building packages..."

# Build only the essential packages first
colcon build --packages-select indoor_nav_bringup

if [ $? -eq 0 ]; then
    print_status "Bringup package built successfully!"
    
    # Source the workspace
    source install/setup.bash
    
    print_status "Ready to launch!"
    echo ""
    echo "Usage:"
    echo "  ros2 launch indoor_nav_bringup indoor_navigation_system.launch.py"
    echo ""
    echo "Note: Some nodes may fail if their packages aren't built yet."
    echo "Run the full setup script for complete build: ./setup_indoor_navigation.sh"
    
else
    print_error "Build failed!"
    echo ""
    echo "Try building individual packages:"
    echo "  colcon build --packages-select lidar_node"
    echo "  colcon build --packages-select ultrasonic_array"
    echo "  colcon build --packages-select indoor_navigation"
    echo "  colcon build --packages-select web_interface"
    exit 1
fi
