#!/usr/bin/env python3

"""
Test script to validate launch file syntax without building packages
"""

import sys
import os

def test_launch_file():
    """Test if launch file has correct syntax"""
    
    launch_file = "src/indoor_nav_bringup/launch/indoor_navigation_system.launch.py"
    
    if not os.path.exists(launch_file):
        print(f"❌ Launch file not found: {launch_file}")
        return False
    
    try:
        # Try to import and parse the launch file
        sys.path.insert(0, os.path.dirname(launch_file))
        
        # Read the file content
        with open(launch_file, 'r') as f:
            content = f.read()
        
        # Basic syntax check
        compile(content, launch_file, 'exec')
        print(f"✅ Launch file syntax is valid: {launch_file}")
        
        # Try to import launch modules
        try:
            from launch import LaunchDescription
            from launch_ros.actions import Node
            from launch.actions import DeclareLaunchArgument
            from launch.substitutions import LaunchConfiguration
            from launch import conditions
            print("✅ All required launch modules are available")
        except ImportError as e:
            print(f"⚠️  Launch module missing: {e}")
            print("   This is normal if ROS2 is not installed")
        
        return True
        
    except SyntaxError as e:
        print(f"❌ Syntax error in launch file: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing launch file: {e}")
        return False

def check_package_structure():
    """Check if package structure is correct"""
    
    packages = [
        "src/indoor_nav_bringup",
        "src/sensors/lidar_node", 
        "src/sensors/ultrasonic_array",
        "src/navigation/indoor_navigation",
        "src/interfaces/web_interface"
    ]
    
    print("\n📁 Checking package structure:")
    
    all_good = True
    for package in packages:
        if os.path.exists(package):
            # Check for required files
            package_xml = os.path.join(package, "package.xml")
            if os.path.exists(package_xml):
                print(f"✅ {package}")
            else:
                print(f"⚠️  {package} (missing package.xml)")
                all_good = False
        else:
            print(f"❌ {package} (not found)")
            all_good = False
    
    return all_good

def main():
    print("🤖 Indoor Navigation System - Launch File Test")
    print("=" * 50)
    
    # Test launch file syntax
    launch_ok = test_launch_file()
    
    # Check package structure
    structure_ok = check_package_structure()
    
    print("\n📋 Summary:")
    if launch_ok and structure_ok:
        print("✅ All checks passed!")
        print("\n🚀 To launch the system (when ROS2 is available):")
        print("   ros2 launch indoor_nav_bringup indoor_navigation_system.launch.py")
        print("\n📝 To build packages:")
        print("   ./setup_indoor_navigation.sh")
    else:
        print("❌ Some issues found. Please check the errors above.")
    
    print("\n💡 If you don't have ROS2 installed, run:")
    print("   chmod +x check_dependencies.sh && ./check_dependencies.sh")

if __name__ == "__main__":
    main()
