[2.637s] Invoking command in '/home/<USER>/indoor_autonomous_vehicle/src/interfaces/web_interface': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/indoor_autonomous_vehicle/build/web_interface/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/web_interface build --build-base /home/<USER>/indoor_autonomous_vehicle/build/web_interface/build install --record /home/<USER>/indoor_autonomous_vehicle/build/web_interface/install.log --single-version-externally-managed install_data
[3.619s] running egg_info
[3.701s] writing ../../../build/web_interface/web_interface.egg-info/PKG-INFO
[3.705s] writing dependency_links to ../../../build/web_interface/web_interface.egg-info/dependency_links.txt
[3.705s] writing entry points to ../../../build/web_interface/web_interface.egg-info/entry_points.txt
[3.705s] writing requirements to ../../../build/web_interface/web_interface.egg-info/requires.txt
[3.705s] writing top-level names to ../../../build/web_interface/web_interface.egg-info/top_level.txt
[3.823s] reading manifest file '../../../build/web_interface/web_interface.egg-info/SOURCES.txt'
[3.836s] writing manifest file '../../../build/web_interface/web_interface.egg-info/SOURCES.txt'
[3.836s] running build
[3.836s] running build_py
[3.837s] copying web_interface/web_server.py -> /home/<USER>/indoor_autonomous_vehicle/build/web_interface/build/lib/web_interface
[3.837s] running install
[3.846s] running install_lib
[3.881s] copying /home/<USER>/indoor_autonomous_vehicle/build/web_interface/build/lib/web_interface/web_server.py -> /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages/web_interface
[3.881s] byte-compiling /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages/web_interface/web_server.py to web_server.cpython-312.pyc
[3.896s] running install_data
[3.897s] running install_egg_info
[3.986s] removing '/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages/web_interface-0.0.0-py3.12.egg-info' (and everything under it)
[3.988s] Copying ../../../build/web_interface/web_interface.egg-info to /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages/web_interface-0.0.0-py3.12.egg-info
[3.989s] running install_scripts
[4.396s] Installing ros_bridge script to /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/web_interface
[4.396s] Installing web_server script to /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/web_interface
[4.396s] writing list of installed files to '/home/<USER>/indoor_autonomous_vehicle/build/web_interface/install.log'
[4.484s] Invoked command in '/home/<USER>/indoor_autonomous_vehicle/src/interfaces/web_interface' returned '0': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/indoor_autonomous_vehicle/build/web_interface/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/web_interface build --build-base /home/<USER>/indoor_autonomous_vehicle/build/web_interface/build install --record /home/<USER>/indoor_autonomous_vehicle/build/web_interface/install.log --single-version-externally-managed install_data
