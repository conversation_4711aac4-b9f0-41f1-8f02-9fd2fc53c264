Invoking command in '/home/<USER>/indoor_autonomous_vehicle/src/interfaces/web_interface': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/indoor_autonomous_vehicle/build/web_interface/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/web_interface build --build-base /home/<USER>/indoor_autonomous_vehicle/build/web_interface/build install --record /home/<USER>/indoor_autonomous_vehicle/build/web_interface/install.log --single-version-externally-managed install_data
Invoked command in '/home/<USER>/indoor_autonomous_vehicle/src/interfaces/web_interface' returned '0': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/indoor_autonomous_vehicle/build/web_interface/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/web_interface build --build-base /home/<USER>/indoor_autonomous_vehicle/build/web_interface/build install --record /home/<USER>/indoor_autonomous_vehicle/build/web_interface/install.log --single-version-externally-managed install_data
