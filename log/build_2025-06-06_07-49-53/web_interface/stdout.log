running egg_info
writing ../../../build/web_interface/web_interface.egg-info/PKG-INFO
writing dependency_links to ../../../build/web_interface/web_interface.egg-info/dependency_links.txt
writing entry points to ../../../build/web_interface/web_interface.egg-info/entry_points.txt
writing requirements to ../../../build/web_interface/web_interface.egg-info/requires.txt
writing top-level names to ../../../build/web_interface/web_interface.egg-info/top_level.txt
reading manifest file '../../../build/web_interface/web_interface.egg-info/SOURCES.txt'
writing manifest file '../../../build/web_interface/web_interface.egg-info/SOURCES.txt'
running build
running build_py
copying web_interface/web_server.py -> /home/<USER>/indoor_autonomous_vehicle/build/web_interface/build/lib/web_interface
running install
running install_lib
copying /home/<USER>/indoor_autonomous_vehicle/build/web_interface/build/lib/web_interface/web_server.py -> /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages/web_interface
byte-compiling /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages/web_interface/web_server.py to web_server.cpython-312.pyc
running install_data
running install_egg_info
removing '/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages/web_interface-0.0.0-py3.12.egg-info' (and everything under it)
Copying ../../../build/web_interface/web_interface.egg-info to /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages/web_interface-0.0.0-py3.12.egg-info
running install_scripts
Installing ros_bridge script to /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/web_interface
Installing web_server script to /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/web_interface
writing list of installed files to '/home/<USER>/indoor_autonomous_vehicle/build/web_interface/install.log'
