[4.308s] Invoking command in '/home/<USER>/indoor_autonomous_vehicle/src/interfaces/web_interface': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/indoor_autonomous_vehicle/build/web_interface/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/web_interface build --build-base /home/<USER>/indoor_autonomous_vehicle/build/web_interface/build install --record /home/<USER>/indoor_autonomous_vehicle/build/web_interface/install.log --single-version-externally-managed install_data
[5.705s] running egg_info
[5.780s] writing ../../../build/web_interface/web_interface.egg-info/PKG-INFO
[5.781s] writing dependency_links to ../../../build/web_interface/web_interface.egg-info/dependency_links.txt
[5.781s] writing entry points to ../../../build/web_interface/web_interface.egg-info/entry_points.txt
[5.782s] writing requirements to ../../../build/web_interface/web_interface.egg-info/requires.txt
[5.782s] writing top-level names to ../../../build/web_interface/web_interface.egg-info/top_level.txt
[5.935s] reading manifest file '../../../build/web_interface/web_interface.egg-info/SOURCES.txt'
[5.953s] writing manifest file '../../../build/web_interface/web_interface.egg-info/SOURCES.txt'
[5.953s] running build
[5.953s] running build_py
[5.970s] copying web_interface/web_server.py -> /home/<USER>/indoor_autonomous_vehicle/build/web_interface/build/lib/web_interface
[5.975s] running install
[6.001s] running install_lib
[6.140s] copying /home/<USER>/indoor_autonomous_vehicle/build/web_interface/build/lib/web_interface/web_server.py -> /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages/web_interface
[6.140s] byte-compiling /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages/web_interface/web_server.py to web_server.cpython-312.pyc
[6.149s] running install_data
[6.158s] running install_egg_info
[6.274s] removing '/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages/web_interface-0.0.0-py3.12.egg-info' (and everything under it)
[6.277s] Copying ../../../build/web_interface/web_interface.egg-info to /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages/web_interface-0.0.0-py3.12.egg-info
[6.297s] running install_scripts
[6.986s] Installing ros_bridge script to /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/web_interface
[6.988s] Installing web_server script to /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/web_interface
[6.990s] writing list of installed files to '/home/<USER>/indoor_autonomous_vehicle/build/web_interface/install.log'
[7.079s] Invoked command in '/home/<USER>/indoor_autonomous_vehicle/src/interfaces/web_interface' returned '0': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/indoor_autonomous_vehicle/build/web_interface/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/web_interface build --build-base /home/<USER>/indoor_autonomous_vehicle/build/web_interface/build install --record /home/<USER>/indoor_autonomous_vehicle/build/web_interface/install.log --single-version-externally-managed install_data
