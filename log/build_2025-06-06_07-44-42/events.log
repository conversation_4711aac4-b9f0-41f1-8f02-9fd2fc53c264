[0.000000] (-) TimerEvent: {}
[0.000113] (-) JobUnselected: {'identifier': 'indoor_nav_bringup'}
[0.000147] (-) JobUnselected: {'identifier': 'indoor_navigation'}
[0.000169] (-) JobUnselected: {'identifier': 'lidar_node'}
[0.000183] (-) JobUnselected: {'identifier': 'ultrasonic_array'}
[0.000197] (web_interface) JobQueued: {'identifier': 'web_interface', 'dependencies': OrderedDict()}
[0.002074] (web_interface) JobStarted: {'identifier': 'web_interface'}
[0.099691] (-) TimerEvent: {}
[0.200135] (-) TimerEvent: {}
[0.309818] (-) TimerEvent: {}
[0.410547] (-) TimerEvent: {}
[0.510988] (-) TimerEvent: {}
[0.611977] (-) TimerEvent: {}
[0.712831] (-) TimerEvent: {}
[0.818888] (-) TimerEvent: {}
[0.919150] (-) TimerEvent: {}
[1.020451] (-) TimerEvent: {}
[1.121327] (-) TimerEvent: {}
[1.222387] (-) TimerEvent: {}
[1.322706] (-) TimerEvent: {}
[1.423295] (-) TimerEvent: {}
[1.524047] (-) TimerEvent: {}
[1.625573] (-) TimerEvent: {}
[1.731237] (-) TimerEvent: {}
[1.836064] (-) TimerEvent: {}
[1.938690] (-) TimerEvent: {}
[2.046917] (-) TimerEvent: {}
[2.148400] (-) TimerEvent: {}
[2.250956] (-) TimerEvent: {}
[2.351139] (-) TimerEvent: {}
[2.451523] (-) TimerEvent: {}
[2.551864] (-) TimerEvent: {}
[2.654445] (-) TimerEvent: {}
[2.756091] (-) TimerEvent: {}
[2.864653] (-) TimerEvent: {}
[2.964975] (-) TimerEvent: {}
[3.065369] (-) TimerEvent: {}
[3.166002] (-) TimerEvent: {}
[3.266301] (-) TimerEvent: {}
[3.366691] (-) TimerEvent: {}
[3.469299] (-) TimerEvent: {}
[3.570528] (-) TimerEvent: {}
[3.670863] (-) TimerEvent: {}
[3.771291] (-) TimerEvent: {}
[3.872576] (-) TimerEvent: {}
[3.973021] (-) TimerEvent: {}
[4.087867] (-) TimerEvent: {}
[4.188146] (-) TimerEvent: {}
[4.290967] (-) TimerEvent: {}
[4.306823] (web_interface) Command: {'cmd': ['/usr/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'egg_info', '--egg-base', '../../../build/web_interface', 'build', '--build-base', '/home/<USER>/indoor_autonomous_vehicle/build/web_interface/build', 'install', '--record', '/home/<USER>/indoor_autonomous_vehicle/build/web_interface/install.log', '--single-version-externally-managed', 'install_data'], 'cwd': '/home/<USER>/indoor_autonomous_vehicle/src/interfaces/web_interface', 'env': {'LESSOPEN': '| /usr/bin/lesspipe %s', 'USER': 'datlq13', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'wayland', 'GIT_ASKPASS': '/usr/share/code/resources/app/extensions/git/dist/askpass.sh', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'LESS': '-FX', 'HOME': '/home/<USER>', 'CHROME_DESKTOP': 'code.desktop', 'OLDPWD': '/home/<USER>/indoor_autonomous_vehicle', 'TERM_PROGRAM_VERSION': '1.100.3', 'DESKTOP_SESSION': 'ubuntu', 'GIO_LAUNCHED_DESKTOP_FILE': '/usr/share/applications/code.desktop', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'PAGER': 'cat', 'VSCODE_GIT_ASKPASS_MAIN': '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_ASKPASS_NODE': '/usr/share/code/code', 'MANAGERPID': '1673', 'DBUS_STARTER_BUS_TYPE': 'session', 'SYSTEMD_EXEC_PID': '1893', 'IM_CONFIG_CHECK_ENV': '1', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus,guid=e3b17c9eca2baa145f47257c6842950f', 'COLORTERM': 'truecolor', 'GIO_LAUNCHED_DESKTOP_FILE_PID': '6488', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'IM_CONFIG_PHASE': '1', 'WAYLAND_DISPLAY': 'wayland-0', 'COLCON_PREFIX_PATH': '/home/<USER>/indoor_autonomous_vehicle/install', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'datlq13', 'JOURNAL_STREAM': '9:14247', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/app.slice/app-gnome\\x2dsession\\x2dmanager.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'datlq13', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin', 'SESSION_MANAGER': 'local/UbuntuOS:@/tmp/.ICE-unix/1893,unix/UbuntuOS:/tmp/.ICE-unix/1893', 'INVOCATION_ID': 'bc36b01aef7f499ea78f46818ffa1a24', 'XDG_MENU_PREFIX': 'gnome-', 'GNOME_SETUP_DISPLAY': ':1', 'XDG_RUNTIME_DIR': '/run/user/1000', 'GDK_BACKEND': 'x11', 'DISPLAY': ':0', 'LANG': 'en_US.UTF-8', 'XDG_CURRENT_DESKTOP': 'Unity', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/.mutter-Xwaylandauth.GH5U72', 'LS_COLORS': '', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-3cddd51f37.sock', 'TERM_PROGRAM': 'vscode', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup:/home/<USER>/indoor_autonomous_vehicle/install/web_interface:/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation:/opt/ros/jazzy', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'SHELL': '/bin/bash', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'GIT_PAGER': 'cat', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/indoor_autonomous_vehicle/build/web_interface', 'LC_ALL': 'en_US.UTF-8', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'DBUS_STARTER_ADDRESS': 'unix:path=/run/user/1000/bus,guid=e3b17c9eca2baa145f47257c6842950f', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/indoor_autonomous_vehicle/build/web_interface/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'CMAKE_PREFIX_PATH': '/home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor'}, 'shell': False}
[4.396460] (-) TimerEvent: {}
[4.498904] (-) TimerEvent: {}
[4.599491] (-) TimerEvent: {}
[4.706874] (-) TimerEvent: {}
[4.807216] (-) TimerEvent: {}
[4.908630] (-) TimerEvent: {}
[5.012497] (-) TimerEvent: {}
[5.114453] (-) TimerEvent: {}
[5.214822] (-) TimerEvent: {}
[5.316492] (-) TimerEvent: {}
[5.416811] (-) TimerEvent: {}
[5.517606] (-) TimerEvent: {}
[5.617965] (-) TimerEvent: {}
[5.706834] (web_interface) StdoutLine: {'line': b'running egg_info\n'}
[5.718041] (-) TimerEvent: {}
[5.781835] (web_interface) StdoutLine: {'line': b'writing ../../../build/web_interface/web_interface.egg-info/PKG-INFO\n'}
[5.782720] (web_interface) StdoutLine: {'line': b'writing dependency_links to ../../../build/web_interface/web_interface.egg-info/dependency_links.txt\n'}
[5.783255] (web_interface) StdoutLine: {'line': b'writing entry points to ../../../build/web_interface/web_interface.egg-info/entry_points.txt\n'}
[5.783822] (web_interface) StdoutLine: {'line': b'writing requirements to ../../../build/web_interface/web_interface.egg-info/requires.txt\n'}
[5.783980] (web_interface) StdoutLine: {'line': b'writing top-level names to ../../../build/web_interface/web_interface.egg-info/top_level.txt\n'}
[5.818172] (-) TimerEvent: {}
[5.919173] (-) TimerEvent: {}
[5.935276] (web_interface) StdoutLine: {'line': b"reading manifest file '../../../build/web_interface/web_interface.egg-info/SOURCES.txt'\n"}
[5.954759] (web_interface) StdoutLine: {'line': b"writing manifest file '../../../build/web_interface/web_interface.egg-info/SOURCES.txt'\n"}
[5.954969] (web_interface) StdoutLine: {'line': b'running build\n'}
[5.955080] (web_interface) StdoutLine: {'line': b'running build_py\n'}
[5.971992] (web_interface) StdoutLine: {'line': b'copying web_interface/web_server.py -> /home/<USER>/indoor_autonomous_vehicle/build/web_interface/build/lib/web_interface\n'}
[5.976930] (web_interface) StdoutLine: {'line': b'running install\n'}
[6.002958] (web_interface) StdoutLine: {'line': b'running install_lib\n'}
[6.019620] (-) TimerEvent: {}
[6.124463] (-) TimerEvent: {}
[6.141529] (web_interface) StdoutLine: {'line': b'copying /home/<USER>/indoor_autonomous_vehicle/build/web_interface/build/lib/web_interface/web_server.py -> /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages/web_interface\n'}
[6.142382] (web_interface) StdoutLine: {'line': b'byte-compiling /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages/web_interface/web_server.py to web_server.cpython-312.pyc\n'}
[6.151496] (web_interface) StdoutLine: {'line': b'running install_data\n'}
[6.160454] (web_interface) StdoutLine: {'line': b'running install_egg_info\n'}
[6.225191] (-) TimerEvent: {}
[6.275768] (web_interface) StdoutLine: {'line': b"removing '/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages/web_interface-0.0.0-py3.12.egg-info' (and everything under it)\n"}
[6.278474] (web_interface) StdoutLine: {'line': b'Copying ../../../build/web_interface/web_interface.egg-info to /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages/web_interface-0.0.0-py3.12.egg-info\n'}
[6.299152] (web_interface) StdoutLine: {'line': b'running install_scripts\n'}
[6.332295] (-) TimerEvent: {}
[6.432610] (-) TimerEvent: {}
[6.535985] (-) TimerEvent: {}
[6.636968] (-) TimerEvent: {}
[6.742340] (-) TimerEvent: {}
[6.842998] (-) TimerEvent: {}
[6.943341] (-) TimerEvent: {}
[6.988346] (web_interface) StdoutLine: {'line': b'Installing ros_bridge script to /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/web_interface\n'}
[6.989610] (web_interface) StdoutLine: {'line': b'Installing web_server script to /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/web_interface\n'}
[6.991415] (web_interface) StdoutLine: {'line': b"writing list of installed files to '/home/<USER>/indoor_autonomous_vehicle/build/web_interface/install.log'\n"}
[7.043416] (-) TimerEvent: {}
[7.081271] (web_interface) CommandEnded: {'returncode': 0}
[7.144122] (-) TimerEvent: {}
[7.181773] (web_interface) JobEnded: {'identifier': 'web_interface', 'rc': 0}
[7.182880] (-) EventReactorShutdown: {}
