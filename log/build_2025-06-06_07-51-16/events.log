[0.000000] (-) TimerEvent: {}
[0.001454] (-) JobUnselected: {'identifier': 'indoor_nav_bringup'}
[0.001493] (-) JobUnselected: {'identifier': 'indoor_navigation'}
[0.001511] (-) JobUnselected: {'identifier': 'lidar_node'}
[0.001524] (-) JobUnselected: {'identifier': 'ultrasonic_array'}
[0.001538] (web_interface) JobQueued: {'identifier': 'web_interface', 'dependencies': OrderedDict()}
[0.001554] (web_interface) JobStarted: {'identifier': 'web_interface'}
[0.099993] (-) TimerEvent: {}
[0.201890] (-) TimerEvent: {}
[0.302186] (-) TimerEvent: {}
[0.402586] (-) TimerEvent: {}
[0.505322] (-) TimerEvent: {}
[0.610542] (-) TimerEvent: {}
[0.710888] (-) TimerEvent: {}
[0.833810] (-) TimerEvent: {}
[0.934208] (-) TimerEvent: {}
[1.034837] (-) TimerEvent: {}
[1.135211] (-) TimerEvent: {}
[1.235536] (-) TimerEvent: {}
[1.352007] (-) TimerEvent: {}
[1.452632] (-) TimerEvent: {}
[1.554239] (-) TimerEvent: {}
[1.656247] (-) TimerEvent: {}
[1.757878] (-) TimerEvent: {}
[1.862574] (-) TimerEvent: {}
[1.963012] (-) TimerEvent: {}
[2.063586] (-) TimerEvent: {}
[2.166152] (-) TimerEvent: {}
[2.266627] (-) TimerEvent: {}
[2.369888] (-) TimerEvent: {}
[2.472887] (-) TimerEvent: {}
[2.575558] (-) TimerEvent: {}
[2.586004] (web_interface) Command: {'cmd': ['/usr/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'egg_info', '--egg-base', '../../../build/web_interface', 'build', '--build-base', '/home/<USER>/indoor_autonomous_vehicle/build/web_interface/build', 'install', '--record', '/home/<USER>/indoor_autonomous_vehicle/build/web_interface/install.log', '--single-version-externally-managed', 'install_data'], 'cwd': '/home/<USER>/indoor_autonomous_vehicle/src/interfaces/web_interface', 'env': {'LESSOPEN': '| /usr/bin/lesspipe %s', 'USER': 'datlq13', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'wayland', 'GIT_ASKPASS': '/usr/share/code/resources/app/extensions/git/dist/askpass.sh', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'LESS': '-FX', 'HOME': '/home/<USER>', 'CHROME_DESKTOP': 'code.desktop', 'OLDPWD': '/home/<USER>/indoor_autonomous_vehicle', 'TERM_PROGRAM_VERSION': '1.100.3', 'DESKTOP_SESSION': 'ubuntu', 'GIO_LAUNCHED_DESKTOP_FILE': '/usr/share/applications/code.desktop', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'PAGER': 'cat', 'VSCODE_GIT_ASKPASS_MAIN': '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_ASKPASS_NODE': '/usr/share/code/code', 'MANAGERPID': '1673', 'DBUS_STARTER_BUS_TYPE': 'session', 'SYSTEMD_EXEC_PID': '1893', 'IM_CONFIG_CHECK_ENV': '1', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus,guid=e3b17c9eca2baa145f47257c6842950f', 'COLORTERM': 'truecolor', 'GIO_LAUNCHED_DESKTOP_FILE_PID': '6488', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'IM_CONFIG_PHASE': '1', 'WAYLAND_DISPLAY': 'wayland-0', 'COLCON_PREFIX_PATH': '/home/<USER>/indoor_autonomous_vehicle/install', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'datlq13', 'JOURNAL_STREAM': '9:14247', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/app.slice/app-gnome\\x2dsession\\x2dmanager.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'datlq13', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin', 'SESSION_MANAGER': 'local/UbuntuOS:@/tmp/.ICE-unix/1893,unix/UbuntuOS:/tmp/.ICE-unix/1893', 'INVOCATION_ID': 'bc36b01aef7f499ea78f46818ffa1a24', 'XDG_MENU_PREFIX': 'gnome-', 'GNOME_SETUP_DISPLAY': ':1', 'XDG_RUNTIME_DIR': '/run/user/1000', 'GDK_BACKEND': 'x11', 'DISPLAY': ':0', 'LANG': 'en_US.UTF-8', 'XDG_CURRENT_DESKTOP': 'Unity', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/.mutter-Xwaylandauth.GH5U72', 'LS_COLORS': '', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-3cddd51f37.sock', 'TERM_PROGRAM': 'vscode', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup:/home/<USER>/indoor_autonomous_vehicle/install/web_interface:/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation:/opt/ros/jazzy', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'SHELL': '/bin/bash', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'GIT_PAGER': 'cat', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/indoor_autonomous_vehicle/build/web_interface', 'LC_ALL': 'en_US.UTF-8', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'DBUS_STARTER_ADDRESS': 'unix:path=/run/user/1000/bus,guid=e3b17c9eca2baa145f47257c6842950f', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/indoor_autonomous_vehicle/build/web_interface/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'CMAKE_PREFIX_PATH': '/home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor'}, 'shell': False}
[2.677002] (-) TimerEvent: {}
[2.779947] (-) TimerEvent: {}
[2.882720] (-) TimerEvent: {}
[2.983395] (-) TimerEvent: {}
[3.084756] (-) TimerEvent: {}
[3.185945] (-) TimerEvent: {}
[3.287006] (-) TimerEvent: {}
[3.388370] (-) TimerEvent: {}
[3.490056] (-) TimerEvent: {}
[3.571710] (web_interface) StdoutLine: {'line': b'running egg_info\n'}
[3.590170] (-) TimerEvent: {}
[3.655533] (web_interface) StdoutLine: {'line': b'writing ../../../build/web_interface/web_interface.egg-info/PKG-INFO\n'}
[3.679164] (web_interface) StdoutLine: {'line': b'writing dependency_links to ../../../build/web_interface/web_interface.egg-info/dependency_links.txt\n'}
[3.679459] (web_interface) StdoutLine: {'line': b'writing entry points to ../../../build/web_interface/web_interface.egg-info/entry_points.txt\n'}
[3.692954] (web_interface) StdoutLine: {'line': b'writing requirements to ../../../build/web_interface/web_interface.egg-info/requires.txt\n'}
[3.693175] (-) TimerEvent: {}
[3.693797] (web_interface) StdoutLine: {'line': b'writing top-level names to ../../../build/web_interface/web_interface.egg-info/top_level.txt\n'}
[3.793401] (-) TimerEvent: {}
[3.826248] (web_interface) StdoutLine: {'line': b"reading manifest file '../../../build/web_interface/web_interface.egg-info/SOURCES.txt'\n"}
[3.833301] (web_interface) StdoutLine: {'line': b"writing manifest file '../../../build/web_interface/web_interface.egg-info/SOURCES.txt'\n"}
[3.834096] (web_interface) StdoutLine: {'line': b'running build\n'}
[3.834178] (web_interface) StdoutLine: {'line': b'running build_py\n'}
[3.834244] (web_interface) StdoutLine: {'line': b'copying web_interface/web_server.py -> /home/<USER>/indoor_autonomous_vehicle/build/web_interface/build/lib/web_interface\n'}
[3.834308] (web_interface) StdoutLine: {'line': b'running install\n'}
[3.857945] (web_interface) StdoutLine: {'line': b'running install_lib\n'}
[3.893543] (-) TimerEvent: {}
[3.935306] (web_interface) StdoutLine: {'line': b'copying /home/<USER>/indoor_autonomous_vehicle/build/web_interface/build/lib/web_interface/web_server.py -> /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages/web_interface\n'}
[3.936752] (web_interface) StdoutLine: {'line': b'byte-compiling /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages/web_interface/web_server.py to web_server.cpython-312.pyc\n'}
[3.941427] (web_interface) StdoutLine: {'line': b'running install_data\n'}
[3.946796] (web_interface) StdoutLine: {'line': b'running install_egg_info\n'}
[3.993683] (-) TimerEvent: {}
[4.026410] (web_interface) StdoutLine: {'line': b"removing '/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages/web_interface-0.0.0-py3.12.egg-info' (and everything under it)\n"}
[4.030672] (web_interface) StdoutLine: {'line': b'Copying ../../../build/web_interface/web_interface.egg-info to /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages/web_interface-0.0.0-py3.12.egg-info\n'}
[4.031806] (web_interface) StdoutLine: {'line': b'running install_scripts\n'}
[4.094880] (-) TimerEvent: {}
[4.197403] (-) TimerEvent: {}
[4.297927] (-) TimerEvent: {}
[4.401202] (-) TimerEvent: {}
[4.432986] (web_interface) StdoutLine: {'line': b'Installing ros_bridge script to /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/web_interface\n'}
[4.434103] (web_interface) StdoutLine: {'line': b'Installing web_server script to /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/web_interface\n'}
[4.434994] (web_interface) StdoutLine: {'line': b"writing list of installed files to '/home/<USER>/indoor_autonomous_vehicle/build/web_interface/install.log'\n"}
[4.504753] (web_interface) CommandEnded: {'returncode': 0}
[4.505102] (-) TimerEvent: {}
[4.554289] (web_interface) JobEnded: {'identifier': 'web_interface', 'rc': 0}
[4.555464] (-) EventReactorShutdown: {}
