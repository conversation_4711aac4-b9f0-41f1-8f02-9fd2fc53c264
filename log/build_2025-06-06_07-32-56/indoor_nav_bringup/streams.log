[0.031s] Invoking command in '/home/<USER>/indoor_autonomous_vehicle/build/indoor_nav_bringup': AMENT_PREFIX_PATH=/home/<USER>/indoor_autonomous_vehicle/install/web_interface:/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/indoor_autonomous_vehicle/build/indoor_nav_bringup -- -j2 -l2
[0.146s] Invoked command in '/home/<USER>/indoor_autonomous_vehicle/build/indoor_nav_bringup' returned '0': AMENT_PREFIX_PATH=/home/<USER>/indoor_autonomous_vehicle/install/web_interface:/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/indoor_autonomous_vehicle/build/indoor_nav_bringup -- -j2 -l2
[0.179s] Invoking command in '/home/<USER>/indoor_autonomous_vehicle/build/indoor_nav_bringup': AMENT_PREFIX_PATH=/home/<USER>/indoor_autonomous_vehicle/install/web_interface:/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/indoor_autonomous_vehicle/build/indoor_nav_bringup
[0.197s] -- Install configuration: ""
[0.197s] -- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup//launch
[0.197s] -- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup//launch/__pycache__
[0.197s] -- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup//launch/__pycache__/indoor_navigation_system.launch.cpython-312.pyc
[0.197s] -- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup//launch/indoor_navigation_system.launch.py
[0.198s] -- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup//launch/simple_test.launch.py
[0.198s] -- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup//config
[0.198s] -- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup//config/sensor_config.yaml
[0.202s] -- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup//config/navigation_params.yaml
[0.202s] -- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/ament_index/resource_index/package_run_dependencies/indoor_nav_bringup
[0.202s] -- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/ament_index/resource_index/parent_prefix_path/indoor_nav_bringup
[0.202s] -- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup/environment/ament_prefix_path.sh
[0.202s] -- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup/environment/ament_prefix_path.dsv
[0.202s] -- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup/environment/path.sh
[0.202s] -- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup/environment/path.dsv
[0.202s] -- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup/local_setup.bash
[0.202s] -- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup/local_setup.sh
[0.203s] -- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup/local_setup.zsh
[0.203s] -- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup/local_setup.dsv
[0.203s] -- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup/package.dsv
[0.203s] -- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/ament_index/resource_index/packages/indoor_nav_bringup
[0.203s] -- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup/cmake/indoor_nav_bringupConfig.cmake
[0.203s] -- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup/cmake/indoor_nav_bringupConfig-version.cmake
[0.203s] -- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup/package.xml
[0.223s] Invoked command in '/home/<USER>/indoor_autonomous_vehicle/build/indoor_nav_bringup' returned '0': AMENT_PREFIX_PATH=/home/<USER>/indoor_autonomous_vehicle/install/web_interface:/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/indoor_autonomous_vehicle/build/indoor_nav_bringup
