Invoking command in '/home/<USER>/indoor_autonomous_vehicle/build/indoor_nav_bringup': AMENT_PREFIX_PATH=/home/<USER>/indoor_autonomous_vehicle/install/web_interface:/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/indoor_autonomous_vehicle/build/indoor_nav_bringup -- -j2 -l2
Invoked command in '/home/<USER>/indoor_autonomous_vehicle/build/indoor_nav_bringup' returned '0': AMENT_PREFIX_PATH=/home/<USER>/indoor_autonomous_vehicle/install/web_interface:/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/indoor_autonomous_vehicle/build/indoor_nav_bringup -- -j2 -l2
Invoking command in '/home/<USER>/indoor_autonomous_vehicle/build/indoor_nav_bringup': AMENT_PREFIX_PATH=/home/<USER>/indoor_autonomous_vehicle/install/web_interface:/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/indoor_autonomous_vehicle/build/indoor_nav_bringup
Invoked command in '/home/<USER>/indoor_autonomous_vehicle/build/indoor_nav_bringup' returned '0': AMENT_PREFIX_PATH=/home/<USER>/indoor_autonomous_vehicle/install/web_interface:/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/indoor_autonomous_vehicle/build/indoor_nav_bringup
