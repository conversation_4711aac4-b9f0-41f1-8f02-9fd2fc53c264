running egg_info
writing ../../../build/indoor_navigation/indoor_navigation.egg-info/PKG-INFO
writing dependency_links to ../../../build/indoor_navigation/indoor_navigation.egg-info/dependency_links.txt
writing entry points to ../../../build/indoor_navigation/indoor_navigation.egg-info/entry_points.txt
writing requirements to ../../../build/indoor_navigation/indoor_navigation.egg-info/requires.txt
writing top-level names to ../../../build/indoor_navigation/indoor_navigation.egg-info/top_level.txt
reading manifest file '../../../build/indoor_navigation/indoor_navigation.egg-info/SOURCES.txt'
writing manifest file '../../../build/indoor_navigation/indoor_navigation.egg-info/SOURCES.txt'
running build
running build_py
running install
running install_lib
running install_data
running install_egg_info
removing '/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/python3.12/site-packages/indoor_navigation-0.0.0-py3.12.egg-info' (and everything under it)
Copying ../../../build/indoor_navigation/indoor_navigation.egg-info to /home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/python3.12/site-packages/indoor_navigation-0.0.0-py3.12.egg-info
running install_scripts
Installing mission_executor script to /home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/indoor_navigation
Installing navigation_manager script to /home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/indoor_navigation
Installing waypoint_manager script to /home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/indoor_navigation
writing list of installed files to '/home/<USER>/indoor_autonomous_vehicle/build/indoor_navigation/install.log'
