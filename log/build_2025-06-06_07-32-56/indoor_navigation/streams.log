[4.621s] Invoking command in '/home/<USER>/indoor_autonomous_vehicle/src/navigation/indoor_navigation': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/indoor_autonomous_vehicle/build/indoor_navigation/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/indoor_navigation build --build-base /home/<USER>/indoor_autonomous_vehicle/build/indoor_navigation/build install --record /home/<USER>/indoor_autonomous_vehicle/build/indoor_navigation/install.log --single-version-externally-managed install_data
[5.831s] running egg_info
[5.931s] writing ../../../build/indoor_navigation/indoor_navigation.egg-info/PKG-INFO
[5.931s] writing dependency_links to ../../../build/indoor_navigation/indoor_navigation.egg-info/dependency_links.txt
[5.931s] writing entry points to ../../../build/indoor_navigation/indoor_navigation.egg-info/entry_points.txt
[5.931s] writing requirements to ../../../build/indoor_navigation/indoor_navigation.egg-info/requires.txt
[5.932s] writing top-level names to ../../../build/indoor_navigation/indoor_navigation.egg-info/top_level.txt
[6.106s] reading manifest file '../../../build/indoor_navigation/indoor_navigation.egg-info/SOURCES.txt'
[6.131s] writing manifest file '../../../build/indoor_navigation/indoor_navigation.egg-info/SOURCES.txt'
[6.131s] running build
[6.132s] running build_py
[6.136s] running install
[6.156s] running install_lib
[6.247s] running install_data
[6.247s] running install_egg_info
[6.393s] removing '/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/python3.12/site-packages/indoor_navigation-0.0.0-py3.12.egg-info' (and everything under it)
[6.395s] Copying ../../../build/indoor_navigation/indoor_navigation.egg-info to /home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/python3.12/site-packages/indoor_navigation-0.0.0-py3.12.egg-info
[6.404s] running install_scripts
[6.939s] Installing mission_executor script to /home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/indoor_navigation
[6.939s] Installing navigation_manager script to /home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/indoor_navigation
[6.939s] Installing waypoint_manager script to /home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/indoor_navigation
[6.939s] writing list of installed files to '/home/<USER>/indoor_autonomous_vehicle/build/indoor_navigation/install.log'
[7.183s] Invoked command in '/home/<USER>/indoor_autonomous_vehicle/src/navigation/indoor_navigation' returned '0': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/indoor_autonomous_vehicle/build/indoor_navigation/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/indoor_navigation build --build-base /home/<USER>/indoor_autonomous_vehicle/build/indoor_navigation/build install --record /home/<USER>/indoor_autonomous_vehicle/build/indoor_navigation/install.log --single-version-externally-managed install_data
