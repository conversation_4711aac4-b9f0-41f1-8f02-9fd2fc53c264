Invoking command in '/home/<USER>/indoor_autonomous_vehicle/src/navigation/indoor_navigation': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/indoor_autonomous_vehicle/build/indoor_navigation/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/indoor_navigation build --build-base /home/<USER>/indoor_autonomous_vehicle/build/indoor_navigation/build install --record /home/<USER>/indoor_autonomous_vehicle/build/indoor_navigation/install.log --single-version-externally-managed install_data
Invoked command in '/home/<USER>/indoor_autonomous_vehicle/src/navigation/indoor_navigation' returned '0': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/indoor_autonomous_vehicle/build/indoor_navigation/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/indoor_navigation build --build-base /home/<USER>/indoor_autonomous_vehicle/build/indoor_navigation/build install --record /home/<USER>/indoor_autonomous_vehicle/build/indoor_navigation/install.log --single-version-externally-managed install_data
