running egg_info
writing ../../../build/ultrasonic_array/ultrasonic_array.egg-info/PKG-INFO
writing dependency_links to ../../../build/ultrasonic_array/ultrasonic_array.egg-info/dependency_links.txt
writing entry points to ../../../build/ultrasonic_array/ultrasonic_array.egg-info/entry_points.txt
writing requirements to ../../../build/ultrasonic_array/ultrasonic_array.egg-info/requires.txt
writing top-level names to ../../../build/ultrasonic_array/ultrasonic_array.egg-info/top_level.txt
reading manifest file '../../../build/ultrasonic_array/ultrasonic_array.egg-info/SOURCES.txt'
writing manifest file '../../../build/ultrasonic_array/ultrasonic_array.egg-info/SOURCES.txt'
running build
running build_py
running install
running install_lib
running install_data
running install_egg_info
removing '/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array/lib/python3.12/site-packages/ultrasonic_array-0.0.0-py3.12.egg-info' (and everything under it)
Copying ../../../build/ultrasonic_array/ultrasonic_array.egg-info to /home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array/lib/python3.12/site-packages/ultrasonic_array-0.0.0-py3.12.egg-info
running install_scripts
