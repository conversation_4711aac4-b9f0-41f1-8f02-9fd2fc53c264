[3.956s] Invoking command in '/home/<USER>/indoor_autonomous_vehicle/src/interfaces/web_interface': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/indoor_autonomous_vehicle/build/web_interface/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/web_interface build --build-base /home/<USER>/indoor_autonomous_vehicle/build/web_interface/build install --record /home/<USER>/indoor_autonomous_vehicle/build/web_interface/install.log --single-version-externally-managed install_data
[4.721s] running egg_info
[4.837s] writing ../../../build/web_interface/web_interface.egg-info/PKG-INFO
[4.841s] writing dependency_links to ../../../build/web_interface/web_interface.egg-info/dependency_links.txt
[4.841s] writing entry points to ../../../build/web_interface/web_interface.egg-info/entry_points.txt
[4.841s] writing requirements to ../../../build/web_interface/web_interface.egg-info/requires.txt
[4.846s] writing top-level names to ../../../build/web_interface/web_interface.egg-info/top_level.txt
[5.012s] reading manifest file '../../../build/web_interface/web_interface.egg-info/SOURCES.txt'
[5.016s] writing manifest file '../../../build/web_interface/web_interface.egg-info/SOURCES.txt'
[5.018s] running build
[5.018s] running build_py
[5.019s] running install
[5.056s] running install_lib
[5.148s] running install_data
[5.148s] running install_egg_info
[5.289s] removing '/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages/web_interface-0.0.0-py3.12.egg-info' (and everything under it)
[5.290s] Copying ../../../build/web_interface/web_interface.egg-info to /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages/web_interface-0.0.0-py3.12.egg-info
[5.294s] running install_scripts
