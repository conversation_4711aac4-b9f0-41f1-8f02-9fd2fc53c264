[0.000000] (-) TimerEvent: {}
[0.016741] (indoor_navigation) JobQueued: {'identifier': 'indoor_navigation', 'dependencies': OrderedDict()}
[0.016817] (lidar_node) JobQueued: {'identifier': 'lidar_node', 'dependencies': OrderedDict()}
[0.016838] (ultrasonic_array) JobQueued: {'identifier': 'ultrasonic_array', 'dependencies': OrderedDict()}
[0.016853] (web_interface) JobQueued: {'identifier': 'web_interface', 'dependencies': OrderedDict()}
[0.016867] (indoor_nav_bringup) JobQueued: {'identifier': 'indoor_nav_bringup', 'dependencies': OrderedDict({'indoor_navigation': '/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation', 'lidar_node': '/home/<USER>/indoor_autonomous_vehicle/install/lidar_node', 'ultrasonic_array': '/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array', 'web_interface': '/home/<USER>/indoor_autonomous_vehicle/install/web_interface'})}
[0.016901] (indoor_navigation) JobStarted: {'identifier': 'indoor_navigation'}
[0.032181] (lidar_node) JobStarted: {'identifier': 'lidar_node'}
[0.097626] (-) TimerEvent: {}
[0.198061] (-) TimerEvent: {}
[0.298406] (-) TimerEvent: {}
[0.399035] (-) TimerEvent: {}
[0.499699] (-) TimerEvent: {}
[0.600237] (-) TimerEvent: {}
[0.701437] (-) TimerEvent: {}
[0.802536] (-) TimerEvent: {}
[0.904331] (-) TimerEvent: {}
[1.012027] (-) TimerEvent: {}
[1.123177] (-) TimerEvent: {}
[1.223485] (-) TimerEvent: {}
[1.327375] (-) TimerEvent: {}
[1.427795] (-) TimerEvent: {}
[1.532172] (-) TimerEvent: {}
[1.632442] (-) TimerEvent: {}
[1.733670] (-) TimerEvent: {}
[1.835215] (-) TimerEvent: {}
[1.935762] (-) TimerEvent: {}
[2.040691] (-) TimerEvent: {}
[2.141411] (-) TimerEvent: {}
[2.242237] (-) TimerEvent: {}
[2.344861] (-) TimerEvent: {}
[2.446165] (-) TimerEvent: {}
[2.546689] (-) TimerEvent: {}
[2.651354] (-) TimerEvent: {}
[2.751792] (-) TimerEvent: {}
[2.856144] (-) TimerEvent: {}
[2.957574] (-) TimerEvent: {}
[3.063258] (-) TimerEvent: {}
[3.164213] (-) TimerEvent: {}
[3.266015] (-) TimerEvent: {}
[3.366497] (-) TimerEvent: {}
[3.467244] (-) TimerEvent: {}
[3.568714] (-) TimerEvent: {}
[3.671739] (-) TimerEvent: {}
[3.772100] (-) TimerEvent: {}
[3.872931] (-) TimerEvent: {}
[3.975365] (-) TimerEvent: {}
[4.080764] (-) TimerEvent: {}
[4.181094] (-) TimerEvent: {}
[4.281479] (-) TimerEvent: {}
[4.382890] (-) TimerEvent: {}
[4.418148] (lidar_node) Command: {'cmd': ['/usr/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'egg_info', '--egg-base', '../../../build/lidar_node', 'build', '--build-base', '/home/<USER>/indoor_autonomous_vehicle/build/lidar_node/build', 'install', '--record', '/home/<USER>/indoor_autonomous_vehicle/build/lidar_node/install.log', '--single-version-externally-managed', 'install_data'], 'cwd': '/home/<USER>/indoor_autonomous_vehicle/src/sensors/lidar_node', 'env': {'LESSOPEN': '| /usr/bin/lesspipe %s', 'USER': 'datlq13', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'wayland', 'GIT_ASKPASS': '/usr/share/code/resources/app/extensions/git/dist/askpass.sh', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'LESS': '-FX', 'HOME': '/home/<USER>', 'CHROME_DESKTOP': 'code.desktop', 'OLDPWD': '/home/<USER>/indoor_autonomous_vehicle', 'TERM_PROGRAM_VERSION': '1.100.3', 'DESKTOP_SESSION': 'ubuntu', 'GIO_LAUNCHED_DESKTOP_FILE': '/usr/share/applications/code.desktop', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'PAGER': 'cat', 'VSCODE_GIT_ASKPASS_MAIN': '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_ASKPASS_NODE': '/usr/share/code/code', 'MANAGERPID': '1673', 'DBUS_STARTER_BUS_TYPE': 'session', 'SYSTEMD_EXEC_PID': '1893', 'IM_CONFIG_CHECK_ENV': '1', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus,guid=e3b17c9eca2baa145f47257c6842950f', 'COLORTERM': 'truecolor', 'GIO_LAUNCHED_DESKTOP_FILE_PID': '6488', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'IM_CONFIG_PHASE': '1', 'WAYLAND_DISPLAY': 'wayland-0', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'datlq13', 'JOURNAL_STREAM': '9:14247', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/app.slice/app-gnome\\x2dsession\\x2dmanager.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'datlq13', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin', 'SESSION_MANAGER': 'local/UbuntuOS:@/tmp/.ICE-unix/1893,unix/UbuntuOS:/tmp/.ICE-unix/1893', 'INVOCATION_ID': 'bc36b01aef7f499ea78f46818ffa1a24', 'XDG_MENU_PREFIX': 'gnome-', 'GNOME_SETUP_DISPLAY': ':1', 'XDG_RUNTIME_DIR': '/run/user/1000', 'GDK_BACKEND': 'x11', 'DISPLAY': ':0', 'LANG': 'en_US.UTF-8', 'XDG_CURRENT_DESKTOP': 'Unity', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/.mutter-Xwaylandauth.GH5U72', 'LS_COLORS': '', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-3cddd51f37.sock', 'TERM_PROGRAM': 'vscode', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'SHELL': '/bin/bash', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'GIT_PAGER': 'cat', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/indoor_autonomous_vehicle/build/lidar_node', 'LC_ALL': 'en_US.UTF-8', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'DBUS_STARTER_ADDRESS': 'unix:path=/run/user/1000/bus,guid=e3b17c9eca2baa145f47257c6842950f', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/indoor_autonomous_vehicle/build/lidar_node/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor'}, 'shell': False}
[4.484169] (-) TimerEvent: {}
[4.584760] (-) TimerEvent: {}
[4.635706] (indoor_navigation) Command: {'cmd': ['/usr/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'egg_info', '--egg-base', '../../../build/indoor_navigation', 'build', '--build-base', '/home/<USER>/indoor_autonomous_vehicle/build/indoor_navigation/build', 'install', '--record', '/home/<USER>/indoor_autonomous_vehicle/build/indoor_navigation/install.log', '--single-version-externally-managed', 'install_data'], 'cwd': '/home/<USER>/indoor_autonomous_vehicle/src/navigation/indoor_navigation', 'env': {'LESSOPEN': '| /usr/bin/lesspipe %s', 'USER': 'datlq13', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'wayland', 'GIT_ASKPASS': '/usr/share/code/resources/app/extensions/git/dist/askpass.sh', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'LESS': '-FX', 'HOME': '/home/<USER>', 'CHROME_DESKTOP': 'code.desktop', 'OLDPWD': '/home/<USER>/indoor_autonomous_vehicle', 'TERM_PROGRAM_VERSION': '1.100.3', 'DESKTOP_SESSION': 'ubuntu', 'GIO_LAUNCHED_DESKTOP_FILE': '/usr/share/applications/code.desktop', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'PAGER': 'cat', 'VSCODE_GIT_ASKPASS_MAIN': '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_ASKPASS_NODE': '/usr/share/code/code', 'MANAGERPID': '1673', 'DBUS_STARTER_BUS_TYPE': 'session', 'SYSTEMD_EXEC_PID': '1893', 'IM_CONFIG_CHECK_ENV': '1', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus,guid=e3b17c9eca2baa145f47257c6842950f', 'COLORTERM': 'truecolor', 'GIO_LAUNCHED_DESKTOP_FILE_PID': '6488', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'IM_CONFIG_PHASE': '1', 'WAYLAND_DISPLAY': 'wayland-0', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'datlq13', 'JOURNAL_STREAM': '9:14247', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/app.slice/app-gnome\\x2dsession\\x2dmanager.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'datlq13', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin', 'SESSION_MANAGER': 'local/UbuntuOS:@/tmp/.ICE-unix/1893,unix/UbuntuOS:/tmp/.ICE-unix/1893', 'INVOCATION_ID': 'bc36b01aef7f499ea78f46818ffa1a24', 'XDG_MENU_PREFIX': 'gnome-', 'GNOME_SETUP_DISPLAY': ':1', 'XDG_RUNTIME_DIR': '/run/user/1000', 'GDK_BACKEND': 'x11', 'DISPLAY': ':0', 'LANG': 'en_US.UTF-8', 'XDG_CURRENT_DESKTOP': 'Unity', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/.mutter-Xwaylandauth.GH5U72', 'LS_COLORS': '', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-3cddd51f37.sock', 'TERM_PROGRAM': 'vscode', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'SHELL': '/bin/bash', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'GIT_PAGER': 'cat', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/indoor_autonomous_vehicle/build/indoor_navigation', 'LC_ALL': 'en_US.UTF-8', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'DBUS_STARTER_ADDRESS': 'unix:path=/run/user/1000/bus,guid=e3b17c9eca2baa145f47257c6842950f', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/indoor_autonomous_vehicle/build/indoor_navigation/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor'}, 'shell': False}
[4.685181] (-) TimerEvent: {}
[4.788347] (-) TimerEvent: {}
[4.888823] (-) TimerEvent: {}
[4.994763] (-) TimerEvent: {}
[5.098883] (-) TimerEvent: {}
[5.199240] (-) TimerEvent: {}
[5.299784] (-) TimerEvent: {}
[5.400564] (-) TimerEvent: {}
[5.502192] (-) TimerEvent: {}
[5.604297] (-) TimerEvent: {}
[5.705654] (-) TimerEvent: {}
[5.806873] (-) TimerEvent: {}
[5.847205] (indoor_navigation) StdoutLine: {'line': b'running egg_info\n'}
[5.867563] (lidar_node) StdoutLine: {'line': b'running egg_info\n'}
[5.907106] (-) TimerEvent: {}
[5.947449] (indoor_navigation) StdoutLine: {'line': b'writing ../../../build/indoor_navigation/indoor_navigation.egg-info/PKG-INFO\n'}
[5.947789] (indoor_navigation) StdoutLine: {'line': b'writing dependency_links to ../../../build/indoor_navigation/indoor_navigation.egg-info/dependency_links.txt\n'}
[5.948121] (indoor_navigation) StdoutLine: {'line': b'writing entry points to ../../../build/indoor_navigation/indoor_navigation.egg-info/entry_points.txt\n'}
[5.948324] (indoor_navigation) StdoutLine: {'line': b'writing requirements to ../../../build/indoor_navigation/indoor_navigation.egg-info/requires.txt\n'}
[5.948464] (indoor_navigation) StdoutLine: {'line': b'writing top-level names to ../../../build/indoor_navigation/indoor_navigation.egg-info/top_level.txt\n'}
[5.971682] (lidar_node) StdoutLine: {'line': b'writing ../../../build/lidar_node/lidar_node.egg-info/PKG-INFO\n'}
[5.972201] (lidar_node) StdoutLine: {'line': b'writing dependency_links to ../../../build/lidar_node/lidar_node.egg-info/dependency_links.txt\n'}
[5.977194] (lidar_node) StdoutLine: {'line': b'writing entry points to ../../../build/lidar_node/lidar_node.egg-info/entry_points.txt\n'}
[5.977349] (lidar_node) StdoutLine: {'line': b'writing requirements to ../../../build/lidar_node/lidar_node.egg-info/requires.txt\n'}
[5.977423] (lidar_node) StdoutLine: {'line': b'writing top-level names to ../../../build/lidar_node/lidar_node.egg-info/top_level.txt\n'}
[6.010312] (-) TimerEvent: {}
[6.120161] (-) TimerEvent: {}
[6.123196] (indoor_navigation) StdoutLine: {'line': b"reading manifest file '../../../build/indoor_navigation/indoor_navigation.egg-info/SOURCES.txt'\n"}
[6.131293] (lidar_node) StdoutLine: {'line': b"reading manifest file '../../../build/lidar_node/lidar_node.egg-info/SOURCES.txt'\n"}
[6.146736] (lidar_node) StdoutLine: {'line': b"writing manifest file '../../../build/lidar_node/lidar_node.egg-info/SOURCES.txt'\n"}
[6.147953] (indoor_navigation) StdoutLine: {'line': b"writing manifest file '../../../build/indoor_navigation/indoor_navigation.egg-info/SOURCES.txt'\n"}
[6.148302] (indoor_navigation) StdoutLine: {'line': b'running build\n'}
[6.149206] (indoor_navigation) StdoutLine: {'line': b'running build_py\n'}
[6.149564] (lidar_node) StdoutLine: {'line': b'running build\n'}
[6.150142] (lidar_node) StdoutLine: {'line': b'running build_py\n'}
[6.150520] (lidar_node) StdoutLine: {'line': b'running install\n'}
[6.152521] (indoor_navigation) StdoutLine: {'line': b'running install\n'}
[6.169967] (lidar_node) StdoutLine: {'line': b'running install_lib\n'}
[6.173191] (indoor_navigation) StdoutLine: {'line': b'running install_lib\n'}
[6.220284] (-) TimerEvent: {}
[6.239246] (lidar_node) StdoutLine: {'line': b'running install_data\n'}
[6.248392] (lidar_node) StdoutLine: {'line': b'running install_egg_info\n'}
[6.263722] (indoor_navigation) StdoutLine: {'line': b'running install_data\n'}
[6.264132] (indoor_navigation) StdoutLine: {'line': b'running install_egg_info\n'}
[6.323842] (-) TimerEvent: {}
[6.330254] (lidar_node) StdoutLine: {'line': b"removing '/home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/python3.12/site-packages/lidar_node-0.0.0-py3.12.egg-info' (and everything under it)\n"}
[6.336692] (lidar_node) StdoutLine: {'line': b'Copying ../../../build/lidar_node/lidar_node.egg-info to /home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/python3.12/site-packages/lidar_node-0.0.0-py3.12.egg-info\n'}
[6.360990] (lidar_node) StdoutLine: {'line': b'running install_scripts\n'}
[6.409318] (indoor_navigation) StdoutLine: {'line': b"removing '/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/python3.12/site-packages/indoor_navigation-0.0.0-py3.12.egg-info' (and everything under it)\n"}
[6.412279] (indoor_navigation) StdoutLine: {'line': b'Copying ../../../build/indoor_navigation/indoor_navigation.egg-info to /home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/python3.12/site-packages/indoor_navigation-0.0.0-py3.12.egg-info\n'}
[6.421149] (indoor_navigation) StdoutLine: {'line': b'running install_scripts\n'}
[6.424447] (-) TimerEvent: {}
[6.527814] (-) TimerEvent: {}
[6.630138] (-) TimerEvent: {}
[6.730685] (-) TimerEvent: {}
[6.836420] (-) TimerEvent: {}
[6.909031] (lidar_node) StdoutLine: {'line': b'Installing lidar_node script to /home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/lidar_node\n'}
[6.910291] (lidar_node) StdoutLine: {'line': b'Installing lidar_processor script to /home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/lidar_node\n'}
[6.911662] (lidar_node) StdoutLine: {'line': b"writing list of installed files to '/home/<USER>/indoor_autonomous_vehicle/build/lidar_node/install.log'\n"}
[6.936548] (-) TimerEvent: {}
[6.955508] (indoor_navigation) StdoutLine: {'line': b'Installing mission_executor script to /home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/indoor_navigation\n'}
[6.955770] (indoor_navigation) StdoutLine: {'line': b'Installing navigation_manager script to /home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/indoor_navigation\n'}
[6.955847] (indoor_navigation) StdoutLine: {'line': b'Installing waypoint_manager script to /home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/indoor_navigation\n'}
[6.955914] (indoor_navigation) StdoutLine: {'line': b"writing list of installed files to '/home/<USER>/indoor_autonomous_vehicle/build/indoor_navigation/install.log'\n"}
[7.038169] (-) TimerEvent: {}
[7.050154] (lidar_node) CommandEnded: {'returncode': 0}
[7.154294] (-) TimerEvent: {}
[7.158733] (lidar_node) JobEnded: {'identifier': 'lidar_node', 'rc': 0}
[7.159198] (ultrasonic_array) JobStarted: {'identifier': 'ultrasonic_array'}
[7.199760] (indoor_navigation) CommandEnded: {'returncode': 0}
[7.236115] (indoor_navigation) JobEnded: {'identifier': 'indoor_navigation', 'rc': 0}
[7.236663] (web_interface) JobStarted: {'identifier': 'web_interface'}
[7.255915] (-) TimerEvent: {}
[7.356511] (-) TimerEvent: {}
[7.464163] (-) TimerEvent: {}
[7.564417] (-) TimerEvent: {}
[7.664758] (-) TimerEvent: {}
[7.765223] (-) TimerEvent: {}
[7.865733] (-) TimerEvent: {}
[7.966139] (-) TimerEvent: {}
[8.066754] (-) TimerEvent: {}
[8.167145] (-) TimerEvent: {}
[8.269144] (-) TimerEvent: {}
[8.369405] (-) TimerEvent: {}
[8.469767] (-) TimerEvent: {}
[8.571358] (-) TimerEvent: {}
[8.674156] (-) TimerEvent: {}
[8.774944] (-) TimerEvent: {}
[8.875672] (-) TimerEvent: {}
[8.976017] (-) TimerEvent: {}
[9.076475] (-) TimerEvent: {}
[9.186526] (-) TimerEvent: {}
[9.292033] (-) TimerEvent: {}
[9.395777] (-) TimerEvent: {}
[9.498180] (-) TimerEvent: {}
[9.598763] (-) TimerEvent: {}
[9.701717] (-) TimerEvent: {}
[9.802003] (-) TimerEvent: {}
[9.902584] (-) TimerEvent: {}
[10.003444] (-) TimerEvent: {}
[10.105575] (-) TimerEvent: {}
[10.206216] (-) TimerEvent: {}
[10.311416] (-) TimerEvent: {}
[10.411793] (-) TimerEvent: {}
[10.514167] (-) TimerEvent: {}
[10.614745] (-) TimerEvent: {}
[10.715692] (-) TimerEvent: {}
[10.816297] (-) TimerEvent: {}
[10.884613] (ultrasonic_array) Command: {'cmd': ['/usr/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'egg_info', '--egg-base', '../../../build/ultrasonic_array', 'build', '--build-base', '/home/<USER>/indoor_autonomous_vehicle/build/ultrasonic_array/build', 'install', '--record', '/home/<USER>/indoor_autonomous_vehicle/build/ultrasonic_array/install.log', '--single-version-externally-managed', 'install_data'], 'cwd': '/home/<USER>/indoor_autonomous_vehicle/src/sensors/ultrasonic_array', 'env': {'LESSOPEN': '| /usr/bin/lesspipe %s', 'USER': 'datlq13', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'wayland', 'GIT_ASKPASS': '/usr/share/code/resources/app/extensions/git/dist/askpass.sh', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'LESS': '-FX', 'HOME': '/home/<USER>', 'CHROME_DESKTOP': 'code.desktop', 'OLDPWD': '/home/<USER>/indoor_autonomous_vehicle', 'TERM_PROGRAM_VERSION': '1.100.3', 'DESKTOP_SESSION': 'ubuntu', 'GIO_LAUNCHED_DESKTOP_FILE': '/usr/share/applications/code.desktop', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'PAGER': 'cat', 'VSCODE_GIT_ASKPASS_MAIN': '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_ASKPASS_NODE': '/usr/share/code/code', 'MANAGERPID': '1673', 'DBUS_STARTER_BUS_TYPE': 'session', 'SYSTEMD_EXEC_PID': '1893', 'IM_CONFIG_CHECK_ENV': '1', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus,guid=e3b17c9eca2baa145f47257c6842950f', 'COLORTERM': 'truecolor', 'GIO_LAUNCHED_DESKTOP_FILE_PID': '6488', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'IM_CONFIG_PHASE': '1', 'WAYLAND_DISPLAY': 'wayland-0', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'datlq13', 'JOURNAL_STREAM': '9:14247', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/app.slice/app-gnome\\x2dsession\\x2dmanager.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'datlq13', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin', 'SESSION_MANAGER': 'local/UbuntuOS:@/tmp/.ICE-unix/1893,unix/UbuntuOS:/tmp/.ICE-unix/1893', 'INVOCATION_ID': 'bc36b01aef7f499ea78f46818ffa1a24', 'XDG_MENU_PREFIX': 'gnome-', 'GNOME_SETUP_DISPLAY': ':1', 'XDG_RUNTIME_DIR': '/run/user/1000', 'GDK_BACKEND': 'x11', 'DISPLAY': ':0', 'LANG': 'en_US.UTF-8', 'XDG_CURRENT_DESKTOP': 'Unity', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/.mutter-Xwaylandauth.GH5U72', 'LS_COLORS': '', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-3cddd51f37.sock', 'TERM_PROGRAM': 'vscode', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'SHELL': '/bin/bash', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'GIT_PAGER': 'cat', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/indoor_autonomous_vehicle/build/ultrasonic_array', 'LC_ALL': 'en_US.UTF-8', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'DBUS_STARTER_ADDRESS': 'unix:path=/run/user/1000/bus,guid=e3b17c9eca2baa145f47257c6842950f', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/indoor_autonomous_vehicle/build/ultrasonic_array/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor'}, 'shell': False}
[10.918129] (-) TimerEvent: {}
[11.018480] (-) TimerEvent: {}
[11.118888] (-) TimerEvent: {}
[11.192116] (web_interface) Command: {'cmd': ['/usr/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'egg_info', '--egg-base', '../../../build/web_interface', 'build', '--build-base', '/home/<USER>/indoor_autonomous_vehicle/build/web_interface/build', 'install', '--record', '/home/<USER>/indoor_autonomous_vehicle/build/web_interface/install.log', '--single-version-externally-managed', 'install_data'], 'cwd': '/home/<USER>/indoor_autonomous_vehicle/src/interfaces/web_interface', 'env': {'LESSOPEN': '| /usr/bin/lesspipe %s', 'USER': 'datlq13', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'wayland', 'GIT_ASKPASS': '/usr/share/code/resources/app/extensions/git/dist/askpass.sh', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'LESS': '-FX', 'HOME': '/home/<USER>', 'CHROME_DESKTOP': 'code.desktop', 'OLDPWD': '/home/<USER>/indoor_autonomous_vehicle', 'TERM_PROGRAM_VERSION': '1.100.3', 'DESKTOP_SESSION': 'ubuntu', 'GIO_LAUNCHED_DESKTOP_FILE': '/usr/share/applications/code.desktop', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'PAGER': 'cat', 'VSCODE_GIT_ASKPASS_MAIN': '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_ASKPASS_NODE': '/usr/share/code/code', 'MANAGERPID': '1673', 'DBUS_STARTER_BUS_TYPE': 'session', 'SYSTEMD_EXEC_PID': '1893', 'IM_CONFIG_CHECK_ENV': '1', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus,guid=e3b17c9eca2baa145f47257c6842950f', 'COLORTERM': 'truecolor', 'GIO_LAUNCHED_DESKTOP_FILE_PID': '6488', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'IM_CONFIG_PHASE': '1', 'WAYLAND_DISPLAY': 'wayland-0', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'datlq13', 'JOURNAL_STREAM': '9:14247', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/app.slice/app-gnome\\x2dsession\\x2dmanager.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'datlq13', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin', 'SESSION_MANAGER': 'local/UbuntuOS:@/tmp/.ICE-unix/1893,unix/UbuntuOS:/tmp/.ICE-unix/1893', 'INVOCATION_ID': 'bc36b01aef7f499ea78f46818ffa1a24', 'XDG_MENU_PREFIX': 'gnome-', 'GNOME_SETUP_DISPLAY': ':1', 'XDG_RUNTIME_DIR': '/run/user/1000', 'GDK_BACKEND': 'x11', 'DISPLAY': ':0', 'LANG': 'en_US.UTF-8', 'XDG_CURRENT_DESKTOP': 'Unity', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/.mutter-Xwaylandauth.GH5U72', 'LS_COLORS': '', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-3cddd51f37.sock', 'TERM_PROGRAM': 'vscode', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'SHELL': '/bin/bash', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'GIT_PAGER': 'cat', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/indoor_autonomous_vehicle/build/web_interface', 'LC_ALL': 'en_US.UTF-8', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'DBUS_STARTER_ADDRESS': 'unix:path=/run/user/1000/bus,guid=e3b17c9eca2baa145f47257c6842950f', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/indoor_autonomous_vehicle/build/web_interface/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor'}, 'shell': False}
[11.219081] (-) TimerEvent: {}
[11.320170] (-) TimerEvent: {}
[11.425179] (-) TimerEvent: {}
[11.529280] (-) TimerEvent: {}
[11.629584] (-) TimerEvent: {}
[11.730262] (-) TimerEvent: {}
[11.830830] (-) TimerEvent: {}
[11.921523] (ultrasonic_array) StdoutLine: {'line': b'running egg_info\n'}
[11.930967] (-) TimerEvent: {}
[11.957393] (web_interface) StdoutLine: {'line': b'running egg_info\n'}
[12.024190] (ultrasonic_array) StdoutLine: {'line': b'writing ../../../build/ultrasonic_array/ultrasonic_array.egg-info/PKG-INFO\n'}
[12.024345] (ultrasonic_array) StdoutLine: {'line': b'writing dependency_links to ../../../build/ultrasonic_array/ultrasonic_array.egg-info/dependency_links.txt\n'}
[12.024416] (ultrasonic_array) StdoutLine: {'line': b'writing entry points to ../../../build/ultrasonic_array/ultrasonic_array.egg-info/entry_points.txt\n'}
[12.024479] (ultrasonic_array) StdoutLine: {'line': b'writing requirements to ../../../build/ultrasonic_array/ultrasonic_array.egg-info/requires.txt\n'}
[12.024539] (ultrasonic_array) StdoutLine: {'line': b'writing top-level names to ../../../build/ultrasonic_array/ultrasonic_array.egg-info/top_level.txt\n'}
[12.031114] (-) TimerEvent: {}
[12.073247] (web_interface) StdoutLine: {'line': b'writing ../../../build/web_interface/web_interface.egg-info/PKG-INFO\n'}
[12.077235] (web_interface) StdoutLine: {'line': b'writing dependency_links to ../../../build/web_interface/web_interface.egg-info/dependency_links.txt\n'}
[12.077382] (web_interface) StdoutLine: {'line': b'writing entry points to ../../../build/web_interface/web_interface.egg-info/entry_points.txt\n'}
[12.077461] (web_interface) StdoutLine: {'line': b'writing requirements to ../../../build/web_interface/web_interface.egg-info/requires.txt\n'}
[12.083110] (web_interface) StdoutLine: {'line': b'writing top-level names to ../../../build/web_interface/web_interface.egg-info/top_level.txt\n'}
[12.131215] (-) TimerEvent: {}
[12.159181] (ultrasonic_array) StdoutLine: {'line': b"reading manifest file '../../../build/ultrasonic_array/ultrasonic_array.egg-info/SOURCES.txt'\n"}
[12.159318] (ultrasonic_array) StdoutLine: {'line': b"writing manifest file '../../../build/ultrasonic_array/ultrasonic_array.egg-info/SOURCES.txt'\n"}
[12.159823] (ultrasonic_array) StdoutLine: {'line': b'running build\n'}
[12.159955] (ultrasonic_array) StdoutLine: {'line': b'running build_py\n'}
[12.160651] (ultrasonic_array) StdoutLine: {'line': b'running install\n'}
[12.169564] (ultrasonic_array) StdoutLine: {'line': b'running install_lib\n'}
[12.231381] (-) TimerEvent: {}
[12.243195] (ultrasonic_array) StdoutLine: {'line': b'running install_data\n'}
[12.243356] (ultrasonic_array) StdoutLine: {'line': b'running install_egg_info\n'}
[12.248787] (web_interface) StdoutLine: {'line': b"reading manifest file '../../../build/web_interface/web_interface.egg-info/SOURCES.txt'\n"}
[12.252208] (web_interface) StdoutLine: {'line': b"writing manifest file '../../../build/web_interface/web_interface.egg-info/SOURCES.txt'\n"}
[12.254263] (web_interface) StdoutLine: {'line': b'running build\n'}
[12.255049] (web_interface) StdoutLine: {'line': b'running build_py\n'}
[12.255967] (web_interface) StdoutLine: {'line': b'running install\n'}
[12.292594] (web_interface) StdoutLine: {'line': b'running install_lib\n'}
[12.333168] (-) TimerEvent: {}
[12.350191] (ultrasonic_array) StdoutLine: {'line': b"removing '/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array/lib/python3.12/site-packages/ultrasonic_array-0.0.0-py3.12.egg-info' (and everything under it)\n"}
[12.352282] (ultrasonic_array) StdoutLine: {'line': b'Copying ../../../build/ultrasonic_array/ultrasonic_array.egg-info to /home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array/lib/python3.12/site-packages/ultrasonic_array-0.0.0-py3.12.egg-info\n'}
[12.355153] (ultrasonic_array) StdoutLine: {'line': b'running install_scripts\n'}
[12.384101] (web_interface) StdoutLine: {'line': b'running install_data\n'}
[12.384444] (web_interface) StdoutLine: {'line': b'running install_egg_info\n'}
[12.435143] (-) TimerEvent: {}
[12.525119] (web_interface) StdoutLine: {'line': b"removing '/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages/web_interface-0.0.0-py3.12.egg-info' (and everything under it)\n"}
[12.526888] (web_interface) StdoutLine: {'line': b'Copying ../../../build/web_interface/web_interface.egg-info to /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages/web_interface-0.0.0-py3.12.egg-info\n'}
[12.530429] (web_interface) StdoutLine: {'line': b'running install_scripts\n'}
[12.535263] (-) TimerEvent: {}
[12.635684] (-) TimerEvent: {}
[12.749865] (-) TimerEvent: {}
[12.859846] (-) TimerEvent: {}
[12.960323] (-) TimerEvent: {}
[13.032714] (ultrasonic_array) StdoutLine: {'line': b'Installing ultrasonic_array_node script to /home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array/lib/ultrasonic_array\n'}
[13.035638] (ultrasonic_array) StdoutLine: {'line': b"writing list of installed files to '/home/<USER>/indoor_autonomous_vehicle/build/ultrasonic_array/install.log'\n"}
[13.060430] (-) TimerEvent: {}
[13.161369] (-) TimerEvent: {}
[13.225965] (ultrasonic_array) CommandEnded: {'returncode': 0}
[13.237681] (web_interface) StdoutLine: {'line': b'Installing ros_bridge script to /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/web_interface\n'}
[13.237824] (web_interface) StdoutLine: {'line': b'Installing web_server script to /home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/web_interface\n'}
[13.237893] (web_interface) StdoutLine: {'line': b"writing list of installed files to '/home/<USER>/indoor_autonomous_vehicle/build/web_interface/install.log'\n"}
[13.253897] (ultrasonic_array) JobEnded: {'identifier': 'ultrasonic_array', 'rc': 0}
[13.262177] (-) TimerEvent: {}
[13.311573] (web_interface) CommandEnded: {'returncode': 0}
[13.359729] (web_interface) JobEnded: {'identifier': 'web_interface', 'rc': 0}
[13.360272] (indoor_nav_bringup) JobStarted: {'identifier': 'indoor_nav_bringup'}
[13.367998] (-) TimerEvent: {}
[13.389358] (indoor_nav_bringup) JobProgress: {'identifier': 'indoor_nav_bringup', 'progress': 'cmake'}
[13.389709] (indoor_nav_bringup) JobProgress: {'identifier': 'indoor_nav_bringup', 'progress': 'build'}
[13.390816] (indoor_nav_bringup) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/indoor_autonomous_vehicle/build/indoor_nav_bringup', '--', '-j2', '-l2'], 'cwd': '/home/<USER>/indoor_autonomous_vehicle/build/indoor_nav_bringup', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'USER': 'datlq13', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'wayland', 'GIT_ASKPASS': '/usr/share/code/resources/app/extensions/git/dist/askpass.sh', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'LESS': '-FX', 'HOME': '/home/<USER>', 'CHROME_DESKTOP': 'code.desktop', 'OLDPWD': '/home/<USER>/indoor_autonomous_vehicle', 'TERM_PROGRAM_VERSION': '1.100.3', 'DESKTOP_SESSION': 'ubuntu', 'GIO_LAUNCHED_DESKTOP_FILE': '/usr/share/applications/code.desktop', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'PAGER': 'cat', 'VSCODE_GIT_ASKPASS_MAIN': '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_ASKPASS_NODE': '/usr/share/code/code', 'MANAGERPID': '1673', 'DBUS_STARTER_BUS_TYPE': 'session', 'SYSTEMD_EXEC_PID': '1893', 'IM_CONFIG_CHECK_ENV': '1', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus,guid=e3b17c9eca2baa145f47257c6842950f', 'COLORTERM': 'truecolor', 'GIO_LAUNCHED_DESKTOP_FILE_PID': '6488', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'IM_CONFIG_PHASE': '1', 'WAYLAND_DISPLAY': 'wayland-0', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'datlq13', 'JOURNAL_STREAM': '9:14247', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/app.slice/app-gnome\\x2dsession\\x2dmanager.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'datlq13', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin', 'SESSION_MANAGER': 'local/UbuntuOS:@/tmp/.ICE-unix/1893,unix/UbuntuOS:/tmp/.ICE-unix/1893', 'INVOCATION_ID': 'bc36b01aef7f499ea78f46818ffa1a24', 'XDG_MENU_PREFIX': 'gnome-', 'GNOME_SETUP_DISPLAY': ':1', 'XDG_RUNTIME_DIR': '/run/user/1000', 'GDK_BACKEND': 'x11', 'DISPLAY': ':0', 'LANG': 'en_US.UTF-8', 'XDG_CURRENT_DESKTOP': 'Unity', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/.mutter-Xwaylandauth.GH5U72', 'LS_COLORS': '', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-3cddd51f37.sock', 'TERM_PROGRAM': 'vscode', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/home/<USER>/indoor_autonomous_vehicle/install/web_interface:/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation:/opt/ros/jazzy', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'SHELL': '/bin/bash', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'GIT_PAGER': 'cat', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/indoor_autonomous_vehicle/build/indoor_nav_bringup', 'LC_ALL': 'en_US.UTF-8', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'DBUS_STARTER_ADDRESS': 'unix:path=/run/user/1000/bus,guid=e3b17c9eca2baa145f47257c6842950f', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[13.472282] (-) TimerEvent: {}
[13.505755] (indoor_nav_bringup) CommandEnded: {'returncode': 0}
[13.510511] (indoor_nav_bringup) JobProgress: {'identifier': 'indoor_nav_bringup', 'progress': 'install'}
[13.539273] (indoor_nav_bringup) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/indoor_autonomous_vehicle/build/indoor_nav_bringup'], 'cwd': '/home/<USER>/indoor_autonomous_vehicle/build/indoor_nav_bringup', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'USER': 'datlq13', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'wayland', 'GIT_ASKPASS': '/usr/share/code/resources/app/extensions/git/dist/askpass.sh', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'LESS': '-FX', 'HOME': '/home/<USER>', 'CHROME_DESKTOP': 'code.desktop', 'OLDPWD': '/home/<USER>/indoor_autonomous_vehicle', 'TERM_PROGRAM_VERSION': '1.100.3', 'DESKTOP_SESSION': 'ubuntu', 'GIO_LAUNCHED_DESKTOP_FILE': '/usr/share/applications/code.desktop', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'PAGER': 'cat', 'VSCODE_GIT_ASKPASS_MAIN': '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_ASKPASS_NODE': '/usr/share/code/code', 'MANAGERPID': '1673', 'DBUS_STARTER_BUS_TYPE': 'session', 'SYSTEMD_EXEC_PID': '1893', 'IM_CONFIG_CHECK_ENV': '1', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus,guid=e3b17c9eca2baa145f47257c6842950f', 'COLORTERM': 'truecolor', 'GIO_LAUNCHED_DESKTOP_FILE_PID': '6488', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'IM_CONFIG_PHASE': '1', 'WAYLAND_DISPLAY': 'wayland-0', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'datlq13', 'JOURNAL_STREAM': '9:14247', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/app.slice/app-gnome\\x2dsession\\x2dmanager.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'datlq13', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin', 'SESSION_MANAGER': 'local/UbuntuOS:@/tmp/.ICE-unix/1893,unix/UbuntuOS:/tmp/.ICE-unix/1893', 'INVOCATION_ID': 'bc36b01aef7f499ea78f46818ffa1a24', 'XDG_MENU_PREFIX': 'gnome-', 'GNOME_SETUP_DISPLAY': ':1', 'XDG_RUNTIME_DIR': '/run/user/1000', 'GDK_BACKEND': 'x11', 'DISPLAY': ':0', 'LANG': 'en_US.UTF-8', 'XDG_CURRENT_DESKTOP': 'Unity', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/.mutter-Xwaylandauth.GH5U72', 'LS_COLORS': '', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-3cddd51f37.sock', 'TERM_PROGRAM': 'vscode', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/home/<USER>/indoor_autonomous_vehicle/install/web_interface:/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation:/opt/ros/jazzy', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'SHELL': '/bin/bash', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'GIT_PAGER': 'cat', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/indoor_autonomous_vehicle/build/indoor_nav_bringup', 'LC_ALL': 'en_US.UTF-8', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'DBUS_STARTER_ADDRESS': 'unix:path=/run/user/1000/bus,guid=e3b17c9eca2baa145f47257c6842950f', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/indoor_autonomous_vehicle/install/web_interface/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/ultrasonic_array/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/python3.12/site-packages:/home/<USER>/indoor_autonomous_vehicle/install/indoor_navigation/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[13.557416] (indoor_nav_bringup) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[13.557600] (indoor_nav_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup//launch\n'}
[13.557672] (indoor_nav_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup//launch/__pycache__\n'}
[13.557735] (indoor_nav_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup//launch/__pycache__/indoor_navigation_system.launch.cpython-312.pyc\n'}
[13.557804] (indoor_nav_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup//launch/indoor_navigation_system.launch.py\n'}
[13.557865] (indoor_nav_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup//launch/simple_test.launch.py\n'}
[13.557925] (indoor_nav_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup//config\n'}
[13.557985] (indoor_nav_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup//config/sensor_config.yaml\n'}
[13.562126] (indoor_nav_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup//config/navigation_params.yaml\n'}
[13.562333] (indoor_nav_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/ament_index/resource_index/package_run_dependencies/indoor_nav_bringup\n'}
[13.562407] (indoor_nav_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/ament_index/resource_index/parent_prefix_path/indoor_nav_bringup\n'}
[13.562471] (indoor_nav_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup/environment/ament_prefix_path.sh\n'}
[13.562535] (indoor_nav_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup/environment/ament_prefix_path.dsv\n'}
[13.562597] (indoor_nav_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup/environment/path.sh\n'}
[13.562658] (indoor_nav_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup/environment/path.dsv\n'}
[13.562718] (indoor_nav_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup/local_setup.bash\n'}
[13.562777] (indoor_nav_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup/local_setup.sh\n'}
[13.562835] (indoor_nav_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup/local_setup.zsh\n'}
[13.562894] (indoor_nav_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup/local_setup.dsv\n'}
[13.562951] (indoor_nav_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup/package.dsv\n'}
[13.563009] (indoor_nav_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/ament_index/resource_index/packages/indoor_nav_bringup\n'}
[13.563115] (indoor_nav_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup/cmake/indoor_nav_bringupConfig.cmake\n'}
[13.563182] (indoor_nav_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup/cmake/indoor_nav_bringupConfig-version.cmake\n'}
[13.563241] (indoor_nav_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/indoor_autonomous_vehicle/install/indoor_nav_bringup/share/indoor_nav_bringup/package.xml\n'}
[13.583138] (indoor_nav_bringup) CommandEnded: {'returncode': 0}
[13.583551] (-) TimerEvent: {}
[13.603308] (indoor_nav_bringup) JobEnded: {'identifier': 'indoor_nav_bringup', 'rc': 0}
[13.604340] (-) EventReactorShutdown: {}
