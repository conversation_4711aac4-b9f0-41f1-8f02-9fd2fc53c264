[4.387s] Invoking command in '/home/<USER>/indoor_autonomous_vehicle/src/sensors/lidar_node': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/indoor_autonomous_vehicle/build/lidar_node/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/lidar_node build --build-base /home/<USER>/indoor_autonomous_vehicle/build/lidar_node/build install --record /home/<USER>/indoor_autonomous_vehicle/build/lidar_node/install.log --single-version-externally-managed install_data
[5.835s] running egg_info
[5.940s] writing ../../../build/lidar_node/lidar_node.egg-info/PKG-INFO
[5.940s] writing dependency_links to ../../../build/lidar_node/lidar_node.egg-info/dependency_links.txt
[5.945s] writing entry points to ../../../build/lidar_node/lidar_node.egg-info/entry_points.txt
[5.945s] writing requirements to ../../../build/lidar_node/lidar_node.egg-info/requires.txt
[5.945s] writing top-level names to ../../../build/lidar_node/lidar_node.egg-info/top_level.txt
[6.099s] reading manifest file '../../../build/lidar_node/lidar_node.egg-info/SOURCES.txt'
[6.115s] writing manifest file '../../../build/lidar_node/lidar_node.egg-info/SOURCES.txt'
[6.117s] running build
[6.118s] running build_py
[6.118s] running install
[6.138s] running install_lib
[6.207s] running install_data
[6.217s] running install_egg_info
[6.298s] removing '/home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/python3.12/site-packages/lidar_node-0.0.0-py3.12.egg-info' (and everything under it)
[6.305s] Copying ../../../build/lidar_node/lidar_node.egg-info to /home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/python3.12/site-packages/lidar_node-0.0.0-py3.12.egg-info
[6.329s] running install_scripts
[6.877s] Installing lidar_node script to /home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/lidar_node
[6.878s] Installing lidar_processor script to /home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/lidar_node
[6.879s] writing list of installed files to '/home/<USER>/indoor_autonomous_vehicle/build/lidar_node/install.log'
[7.018s] Invoked command in '/home/<USER>/indoor_autonomous_vehicle/src/sensors/lidar_node' returned '0': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/indoor_autonomous_vehicle/build/lidar_node/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/lidar_node build --build-base /home/<USER>/indoor_autonomous_vehicle/build/lidar_node/build install --record /home/<USER>/indoor_autonomous_vehicle/build/lidar_node/install.log --single-version-externally-managed install_data
