running egg_info
writing ../../../build/lidar_node/lidar_node.egg-info/PKG-INFO
writing dependency_links to ../../../build/lidar_node/lidar_node.egg-info/dependency_links.txt
writing entry points to ../../../build/lidar_node/lidar_node.egg-info/entry_points.txt
writing requirements to ../../../build/lidar_node/lidar_node.egg-info/requires.txt
writing top-level names to ../../../build/lidar_node/lidar_node.egg-info/top_level.txt
reading manifest file '../../../build/lidar_node/lidar_node.egg-info/SOURCES.txt'
writing manifest file '../../../build/lidar_node/lidar_node.egg-info/SOURCES.txt'
running build
running build_py
running install
running install_lib
running install_data
running install_egg_info
removing '/home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/python3.12/site-packages/lidar_node-0.0.0-py3.12.egg-info' (and everything under it)
Copying ../../../build/lidar_node/lidar_node.egg-info to /home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/python3.12/site-packages/lidar_node-0.0.0-py3.12.egg-info
running install_scripts
Installing lidar_node script to /home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/lidar_node
Installing lidar_processor script to /home/<USER>/indoor_autonomous_vehicle/install/lidar_node/lib/lidar_node
writing list of installed files to '/home/<USER>/indoor_autonomous_vehicle/build/lidar_node/install.log'
