# 🤖 Indoor Autonomous Vehicle Navigation System

Hệ thống navigation hoàn chỉnh cho xe ARM tự hành trong nhà với khả năng đặt điểm đầu/cuối, tr<PERSON><PERSON> v<PERSON><PERSON> cản, và quản lý waypoint thông minh.

## 🏗️ Kiến trúc hệ thống

```
indoor_autonomous_vehicle/
├── src/
│   ├── sensors/                    # Tầng cảm biến
│   │   ├── lidar_node/            # RPLiDAR cho mapping & obstacle detection
│   │   ├── camera_node/           # Optional: object recognition
│   │   ├── imu_node/              # Chỉ cần gyroscope cho orientation
│   │   └── ultrasonic_array/      # Cảm biến siêu âm bổ sung (4-6 cảm biến)
│   ├── navigation/                # Navigation Stack
│   │   ├── mapping/               # SLAM để tạo bản đồ nhà
│   │   ├── localization/          # Định vị trong bản đồ
│   │   ├── indoor_navigation/     # Main navigation logic
│   │   ├── path_planning/         # A* algorithm cho indoor spaces
│   │   └── obstacle_avoidance/    # Dynamic obstacle avoidance
│   ├── control/                   # Control Layer
│   │   ├── motor_controller/      # Differential drive control
│   │   └── safety_monitor/        # Emergency stop system
│   └── interfaces/                # User Interfaces
│       ├── web_interface/         # Web UI để select destinations
│       ├── voice_command/         # Optional: voice control
│       └── mobile_app/            # Optional: smartphone control
├── launch/                        # Launch files
├── config/                        # Configuration files
└── maps/                          # Saved maps
```

## 🎯 Tính năng chính

### ✅ **Navigation Core**
- **Đặt điểm đầu/cuối**: Web interface để chọn start/end points
- **Multiple waypoints**: Tạo route với nhiều điểm dừng
- **Obstacle avoidance**: Tránh vật cản tĩnh và động
- **Auto replanning**: Tự động tạo lộ trình mới khi bị chặn
- **Recovery system**: Khôi phục khi mất kết nối

### 🗺️ **Mapping & Localization**
- **SLAM mapping**: Tự động tạo bản đồ nhà
- **Persistent maps**: Lưu và tái sử dụng bản đồ
- **Localization**: Định vị chính xác trong bản đồ
- **Map updates**: Cập nhật bản đồ khi có thay đổi

### 📍 **Waypoint Management**
- **Save locations**: Lưu vị trí thường xuyên
- **Smart suggestions**: Gợi ý điểm đến dựa trên lịch sử
- **Frequent locations**: Tự động lưu địa điểm hay đến
- **Mission planning**: Tạo mission với nhiều waypoints

### 🛡️ **Safety & Monitoring**
- **Emergency stop**: Dừng khẩn cấp từ web/voice
- **Collision avoidance**: Tránh va chạm với multiple sensors
- **Health monitoring**: Theo dõi trạng thái hệ thống
- **Recovery behaviors**: Hành vi phục hồi khi gặp sự cố

### 🌐 **User Interfaces**
- **Web dashboard**: Giao diện web responsive
- **Real-time monitoring**: Theo dõi real-time
- **Voice control**: Điều khiển bằng giọng nói (optional)
- **Mobile app**: Ứng dụng smartphone (optional)

## 🚀 Cài đặt và Sử dụng

### 1. **Kiểm tra dependencies**
```bash
cd indoor_autonomous_vehicle/
chmod +x check_dependencies.sh
./check_dependencies.sh
```

### 2. **Validate cấu trúc**
```bash
chmod +x validate_structure.sh
./validate_structure.sh
```

### 3. **Setup hệ thống**
```bash
chmod +x setup_indoor_navigation.sh
./setup_indoor_navigation.sh
```

### 4. **Launch toàn bộ hệ thống**
```bash
# Source ROS2 và workspace
source /opt/ros/humble/setup.bash
source install/setup.bash

# Launch với simulator (cho testing)
ros2 launch indoor_nav_bringup indoor_navigation_system.launch.py

# Launch với hardware thật
ros2 launch indoor_nav_bringup indoor_navigation_system.launch.py use_simulator:=false
```

### 3. **Truy cập Web Interface**
```bash
# Mở browser
http://localhost:8080
```

### 4. **Sử dụng cơ bản**

#### 📍 **Quản lý Waypoints**
```bash
# Lưu vị trí hiện tại
ros2 topic pub /waypoint/save std_msgs/String 'data: "kitchen"'

# Đi đến waypoint
ros2 topic pub /waypoint/goto std_msgs/String 'data: "kitchen"'

# Xem danh sách waypoints
ros2 topic echo /waypoint/list
```

#### 🎯 **Navigation thủ công**
```bash
# Đặt goal position
ros2 topic pub /move_base_simple/goal geometry_msgs/PoseStamped '{
  header: {frame_id: "map"}, 
  pose: {
    position: {x: 2.0, y: 1.0, z: 0.0}, 
    orientation: {w: 1.0}
  }
}'
```

#### 📊 **Monitoring**
```bash
# Trạng thái navigation
ros2 topic echo /navigation/status

# Dữ liệu sensors
ros2 topic echo /scan                    # LiDAR
ros2 topic echo /ultrasonic/proximity    # Ultrasonic
```

## 🔧 Cấu hình Hardware

### **LiDAR (RPLiDAR A1/A2)**
- Port: `/dev/ttyUSB0`
- Baud rate: `115200`
- Range: `0.15m - 12m`
- Frequency: `10Hz`

### **Ultrasonic Array (6 sensors)**
- **Front**: Phát hiện vật cản phía trước
- **Front-Left/Right**: Góc chết
- **Left/Right**: Bên hông
- **Back**: Lùi an toàn
- Range: `0.02m - 4m`
- Frequency: `20Hz`

### **IMU (Optional)**
- Chỉ cần gyroscope cho orientation
- Frequency: `50Hz`

### **Motor Controller**
- Differential drive
- Max speed: `0.5 m/s`
- Max angular: `1.0 rad/s`

## 🎮 Web Interface Features

### **Dashboard chính**
- 🤖 Robot status real-time
- 📍 Current position & goal
- 🗺️ Mission progress
- ⚠️ Alert notifications

### **Waypoint Management**
- 💾 Save current position
- 🎯 Go to saved waypoint
- 🗑️ Delete waypoints
- 📊 Visit statistics

### **Mission Control**
- 🗺️ Create multi-waypoint missions
- ▶️ Start/pause/stop missions
- 📈 Progress tracking
- 🔄 Mission replanning

### **Emergency Controls**
- 🛑 Emergency stop button
- ⏸️ Pause navigation
- ❌ Cancel current mission
- 🔄 Recovery actions

## 📱 Advanced Features

### **Smart Waypoint System**
- **Auto-save frequent locations**: Tự động lưu nơi hay đến
- **Usage statistics**: Thống kê sử dụng waypoints
- **Smart suggestions**: Gợi ý dựa trên thời gian/ngữ cảnh

### **Mission Planning**
- **Multi-stop routes**: Route với nhiều điểm dừng
- **Optimized paths**: Tối ưu hóa đường đi
- **Time scheduling**: Lên lịch missions
- **Repeat missions**: Lặp lại missions định kỳ

### **Recovery & Persistence**
- **Connection recovery**: Tự động kết nối lại
- **Position recovery**: Khôi phục vị trí sau mất điện
- **Mission resume**: Tiếp tục mission sau gián đoạn
- **Data backup**: Backup waypoints và maps

### **Safety Systems**
- **Multi-layer collision avoidance**: LiDAR + Ultrasonic
- **Emergency behaviors**: Hành vi khẩn cấp
- **Fail-safe modes**: Chế độ an toàn
- **Health monitoring**: Giám sát sức khỏe hệ thống

## 🛠️ Customization

### **Launch Options**
```bash
# Disable specific sensors
ros2 launch indoor_nav_bringup indoor_navigation_system.launch.py use_lidar:=false

# Enable camera
ros2 launch indoor_nav_bringup indoor_navigation_system.launch.py use_camera:=true

# Sensors only (no web interface)
ros2 launch indoor_nav_bringup indoor_navigation_system.launch.py use_web_interface:=false
```

### **Configuration Files**
- `config/navigation_params.yaml`: Navigation parameters
- `config/sensor_config.yaml`: Sensor configurations
- `config/safety_params.yaml`: Safety thresholds

## 🔍 Troubleshooting

### **Common Issues**
1. **LiDAR not detected**: Check USB connection and permissions
2. **Web interface not accessible**: Check firewall settings
3. **Navigation stuck**: Check for obstacle detection issues
4. **Waypoints not saving**: Check file permissions in ~/.ros/

### **Debug Commands**
```bash
# Check sensor data
ros2 topic hz /scan
ros2 topic hz /ultrasonic/proximity

# Check navigation status
ros2 topic echo /navigation/status

# View logs
ros2 log view navigation_manager
```

## 📈 Future Enhancements

- 🗣️ **Voice Control**: "Go to kitchen", "Stop", "Come here"
- 📱 **Mobile App**: iOS/Android control app
- 🤖 **Multi-robot**: Coordinate multiple robots
- 🏠 **Smart Home**: Integration với IoT devices
- 📊 **Analytics**: Usage analytics và optimization
- 🎯 **Object Recognition**: Nhận diện objects để navigation

---

**Developed for ARM-based indoor autonomous vehicles with focus on reliability, safety, and ease of use.**
