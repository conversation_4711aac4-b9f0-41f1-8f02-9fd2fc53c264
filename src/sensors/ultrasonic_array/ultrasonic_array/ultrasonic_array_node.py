#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import Range
from std_msgs.msg import Header
import math
import time
import threading
import numpy as np

class UltrasonicArrayNode(Node):
    def __init__(self):
        super().__init__('ultrasonic_array_node')
        
        # Declare parameters
        self.declare_parameter('num_sensors', 6)
        self.declare_parameter('use_simulator', True)
        self.declare_parameter('publish_rate', 20.0)
        self.declare_parameter('max_range', 4.0)
        self.declare_parameter('min_range', 0.02)
        self.declare_parameter('field_of_view', 0.26)  # ~15 degrees
        
        # Get parameters
        self.num_sensors = self.get_parameter('num_sensors').get_parameter_value().integer_value
        self.use_simulator = self.get_parameter('use_simulator').get_parameter_value().bool_value
        self.publish_rate = self.get_parameter('publish_rate').get_parameter_value().double_value
        self.max_range = self.get_parameter('max_range').get_parameter_value().double_value
        self.min_range = self.get_parameter('min_range').get_parameter_value().double_value
        self.field_of_view = self.get_parameter('field_of_view').get_parameter_value().double_value
        
        # Sensor configuration (positions around the robot)
        self.sensor_configs = self.setup_sensor_positions()
        
        # Publishers for each sensor
        self.range_pubs = {}
        for i in range(self.num_sensors):
            topic_name = f'/ultrasonic/sensor_{i}'
            self.range_pubs[i] = self.create_publisher(Range, topic_name, 10)
        
        # Combined proximity publisher
        self.proximity_pub = self.create_publisher(Range, '/ultrasonic/proximity', 10)
        
        # Timer for publishing
        self.timer = self.create_timer(1.0/self.publish_rate, self.publish_sensor_data)
        
        # Simulation obstacles (same as LiDAR for consistency)
        self.obstacles = self.create_obstacles()
        
        self.get_logger().info(f'Ultrasonic array started - {self.num_sensors} sensors, Rate: {self.publish_rate}Hz')
    
    def setup_sensor_positions(self):
        """Setup sensor positions around the robot"""
        configs = {}
        
        if self.num_sensors == 4:
            # 4 sensors: front, back, left, right
            configs[0] = {'angle': 0.0, 'name': 'front', 'frame_id': 'ultrasonic_front'}
            configs[1] = {'angle': math.pi/2, 'name': 'left', 'frame_id': 'ultrasonic_left'}
            configs[2] = {'angle': math.pi, 'name': 'back', 'frame_id': 'ultrasonic_back'}
            configs[3] = {'angle': -math.pi/2, 'name': 'right', 'frame_id': 'ultrasonic_right'}
            
        elif self.num_sensors == 6:
            # 6 sensors: front, front-left, left, back, right, front-right
            configs[0] = {'angle': 0.0, 'name': 'front', 'frame_id': 'ultrasonic_front'}
            configs[1] = {'angle': math.pi/3, 'name': 'front_left', 'frame_id': 'ultrasonic_front_left'}
            configs[2] = {'angle': 2*math.pi/3, 'name': 'left', 'frame_id': 'ultrasonic_left'}
            configs[3] = {'angle': math.pi, 'name': 'back', 'frame_id': 'ultrasonic_back'}
            configs[4] = {'angle': -2*math.pi/3, 'name': 'right', 'frame_id': 'ultrasonic_right'}
            configs[5] = {'angle': -math.pi/3, 'name': 'front_right', 'frame_id': 'ultrasonic_front_right'}
        
        return configs
    
    def create_obstacles(self):
        """Create obstacles for simulation (simplified version of room)"""
        obstacles = []
        
        # Room walls (3m x 3m room for ultrasonic range)
        obstacles.extend([(x, 1.5) for x in np.linspace(-1.5, 1.5, 30)])  # North
        obstacles.extend([(x, -1.5) for x in np.linspace(-1.5, 1.5, 30)])  # South
        obstacles.extend([(1.5, y) for y in np.linspace(-1.5, 1.5, 30)])  # East
        obstacles.extend([(-1.5, y) for y in np.linspace(-1.5, 1.5, 30)])  # West
        
        # Some furniture
        obstacles.extend([(x, 0.5) for x in np.linspace(0.3, 0.8, 5)])  # Table
        obstacles.extend([(-0.8, y) for y in np.linspace(-0.3, 0.3, 6)])  # Chair
        
        return obstacles
    
    def simulate_ultrasonic_reading(self, sensor_angle):
        """Simulate ultrasonic sensor reading in given direction"""
        robot_x, robot_y = 0.0, 0.0  # Robot at origin
        
        min_distance = self.max_range
        
        # Cast multiple rays within field of view
        num_rays = 5
        for i in range(num_rays):
            ray_angle = sensor_angle + (i - num_rays//2) * self.field_of_view / num_rays
            
            # Cast ray and find closest obstacle
            for distance in np.linspace(self.min_range, self.max_range, 50):
                ray_x = robot_x + distance * math.cos(ray_angle)
                ray_y = robot_y + distance * math.sin(ray_angle)
                
                # Check collision with obstacles
                for obs_x, obs_y in self.obstacles:
                    if math.sqrt((ray_x - obs_x)**2 + (ray_y - obs_y)**2) < 0.05:
                        min_distance = min(min_distance, distance)
                        break
                
                if min_distance < self.max_range:
                    break
        
        # Add noise
        noise = np.random.normal(0, 0.01)
        min_distance += noise
        
        # Clamp to valid range
        if min_distance < self.min_range:
            return float('inf')
        elif min_distance > self.max_range:
            return float('inf')
        
        return min_distance
    
    def publish_sensor_data(self):
        """Publish data from all ultrasonic sensors"""
        current_time = self.get_clock().now().to_msg()
        
        all_ranges = []
        
        for sensor_id in range(self.num_sensors):
            config = self.sensor_configs[sensor_id]
            
            # Get sensor reading
            if self.use_simulator:
                range_value = self.simulate_ultrasonic_reading(config['angle'])
            else:
                range_value = self.read_hardware_sensor(sensor_id)
            
            # Create Range message
            range_msg = Range()
            range_msg.header.stamp = current_time
            range_msg.header.frame_id = config['frame_id']
            range_msg.radiation_type = Range.ULTRASOUND
            range_msg.field_of_view = self.field_of_view
            range_msg.min_range = self.min_range
            range_msg.max_range = self.max_range
            range_msg.range = range_value
            
            # Publish individual sensor
            self.range_pubs[sensor_id].publish(range_msg)
            
            # Collect for proximity calculation
            if not math.isinf(range_value):
                all_ranges.append(range_value)
        
        # Publish combined proximity (minimum distance)
        if all_ranges:
            min_proximity = min(all_ranges)
            proximity_msg = Range()
            proximity_msg.header.stamp = current_time
            proximity_msg.header.frame_id = 'base_link'
            proximity_msg.radiation_type = Range.ULTRASOUND
            proximity_msg.field_of_view = 2 * math.pi  # 360 degrees
            proximity_msg.min_range = self.min_range
            proximity_msg.max_range = self.max_range
            proximity_msg.range = min_proximity
            
            self.proximity_pub.publish(proximity_msg)
            
            # Log warnings for close obstacles
            if min_proximity < 0.3:
                self.get_logger().warn(f'Close obstacle detected: {min_proximity:.2f}m')
    
    def read_hardware_sensor(self, sensor_id):
        """Read from hardware ultrasonic sensor"""
        # Placeholder for hardware interface
        # In practice, this would interface with GPIO/I2C/Serial
        return float('inf')

def main(args=None):
    rclpy.init(args=args)
    
    try:
        ultrasonic_node = UltrasonicArrayNode()
        rclpy.spin(ultrasonic_node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'ultrasonic_node' in locals():
            ultrasonic_node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
