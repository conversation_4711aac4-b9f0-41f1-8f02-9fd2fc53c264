#!/usr/bin/env python3

from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration
from launch import conditions

def generate_launch_description():
    # Declare launch arguments
    use_lidar_arg = DeclareLaunchArgument(
        'use_lidar',
        default_value='true',
        description='Enable LiDAR sensor'
    )
    
    use_ultrasonic_arg = DeclareLaunchArgument(
        'use_ultrasonic',
        default_value='true',
        description='Enable ultrasonic sensor array'
    )
    
    use_camera_arg = DeclareLaunchArgument(
        'use_camera',
        default_value='false',
        description='Enable camera sensor'
    )
    
    use_imu_arg = DeclareLaunchArgument(
        'use_imu',
        default_value='true',
        description='Enable IMU sensor'
    )
    
    use_web_interface_arg = DeclareLaunchArgument(
        'use_web_interface',
        default_value='true',
        description='Enable web interface'
    )
    
    use_simulator_arg = DeclareLaunchArgument(
        'use_simulator',
        default_value='true',
        description='Use sensor simulators instead of real hardware'
    )
    
    # Sensor Nodes
    lidar_node = Node(
        package='lidar_node',
        executable='lidar_node',
        name='lidar_node',
        output='screen',
        parameters=[{
            'serial_port': '/dev/ttyUSB0',
            'baud_rate': 115200,
            'frame_id': 'laser_frame',
            'use_simulator': LaunchConfiguration('use_simulator'),
            'scan_frequency': 10.0,
            'range_max': 12.0,
        }],
        condition=conditions.IfCondition(LaunchConfiguration('use_lidar'))
    )
    
    ultrasonic_array_node = Node(
        package='ultrasonic_array',
        executable='ultrasonic_array_node',
        name='ultrasonic_array_node',
        output='screen',
        parameters=[{
            'num_sensors': 6,
            'use_simulator': LaunchConfiguration('use_simulator'),
            'publish_rate': 20.0,
            'max_range': 4.0,
        }],
        condition=conditions.IfCondition(LaunchConfiguration('use_ultrasonic'))
    )
    
    # Navigation Nodes
    navigation_manager_node = Node(
        package='indoor_navigation',
        executable='navigation_manager',
        name='navigation_manager',
        output='screen',
        parameters=[{
            'max_linear_speed': 0.5,
            'max_angular_speed': 1.0,
            'goal_tolerance': 0.2,
            'obstacle_threshold': 0.5,
            'emergency_threshold': 0.3,
        }]
    )
    
    waypoint_manager_node = Node(
        package='indoor_navigation',
        executable='waypoint_manager',
        name='waypoint_manager',
        output='screen',
        parameters=[{
            'waypoints_file': 'waypoints/saved_waypoints.json',
            'auto_save': True,
            'position_tolerance': 0.1,
        }]
    )
    
    # Web Interface
    web_server_node = Node(
        package='web_interface',
        executable='web_server',
        name='web_server',
        output='screen',
        parameters=[{
            'port': 8080,
            'host': '0.0.0.0',
        }],
        condition=conditions.IfCondition(LaunchConfiguration('use_web_interface'))
    )
    
    # Static Transform Publishers (for sensor frames)
    base_to_laser_tf = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='base_to_laser_tf',
        arguments=['0', '0', '0.1', '0', '0', '0', 'base_link', 'laser_frame']
    )
    
    base_to_ultrasonic_front_tf = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='base_to_ultrasonic_front_tf',
        arguments=['0.15', '0', '0.05', '0', '0', '0', 'base_link', 'ultrasonic_front']
    )
    
    base_to_ultrasonic_left_tf = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='base_to_ultrasonic_left_tf',
        arguments=['0', '0.15', '0.05', '0', '0', '1.57', 'base_link', 'ultrasonic_left']
    )
    
    base_to_ultrasonic_right_tf = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='base_to_ultrasonic_right_tf',
        arguments=['0', '-0.15', '0.05', '0', '0', '-1.57', 'base_link', 'ultrasonic_right']
    )
    
    base_to_ultrasonic_back_tf = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='base_to_ultrasonic_back_tf',
        arguments=['-0.15', '0', '0.05', '0', '0', '3.14', 'base_link', 'ultrasonic_back']
    )
    
    # Map to Odom transform (for simulation - in real system use AMCL)
    map_to_odom_tf = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='map_to_odom_tf',
        arguments=['0', '0', '0', '0', '0', '0', 'map', 'odom']
    )
    
    odom_to_base_tf = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='odom_to_base_tf',
        arguments=['0', '0', '0', '0', '0', '0', 'odom', 'base_link']
    )
    
    return LaunchDescription([
        use_lidar_arg,
        use_ultrasonic_arg,
        use_camera_arg,
        use_imu_arg,
        use_web_interface_arg,
        use_simulator_arg,
        
        # Sensor nodes
        lidar_node,
        ultrasonic_array_node,
        
        # Navigation nodes
        navigation_manager_node,
        waypoint_manager_node,
        
        # Interface nodes
        web_server_node,
        
        # Transform publishers
        base_to_laser_tf,
        base_to_ultrasonic_front_tf,
        base_to_ultrasonic_left_tf,
        base_to_ultrasonic_right_tf,
        base_to_ultrasonic_back_tf,
        map_to_odom_tf,
        odom_to_base_tf,
    ])
