<?xml version="1.0"?>
<robot name="indoor_robot">
  
  <!-- Base Link -->
  <link name="base_link">
    <visual>
      <geometry>
        <box size="0.3 0.3 0.1"/>
      </geometry>
      <material name="blue">
        <color rgba="0.0 0.0 1.0 1.0"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <box size="0.3 0.3 0.1"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="10.0"/>
      <inertia ixx="0.1" ixy="0.0" ixz="0.0" iyy="0.1" iyz="0.0" izz="0.1"/>
    </inertial>
  </link>
  
  <!-- LiDAR Link -->
  <link name="laser_frame">
    <visual>
      <geometry>
        <cylinder radius="0.05" length="0.04"/>
      </geometry>
      <material name="black">
        <color rgba="0.0 0.0 0.0 1.0"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <cylinder radius="0.05" length="0.04"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.5"/>
      <inertia ixx="0.01" ixy="0.0" ixz="0.0" iyy="0.01" iyz="0.0" izz="0.01"/>
    </inertial>
  </link>
  
  <!-- LiDAR Joint -->
  <joint name="laser_joint" type="fixed">
    <parent link="base_link"/>
    <child link="laser_frame"/>
    <origin xyz="0.0 0.0 0.1" rpy="0.0 0.0 0.0"/>
  </joint>
  
  <!-- Ultrasonic Sensors -->
  
  <!-- Front Ultrasonic -->
  <link name="ultrasonic_front">
    <visual>
      <geometry>
        <box size="0.02 0.02 0.01"/>
      </geometry>
      <material name="green">
        <color rgba="0.0 1.0 0.0 1.0"/>
      </material>
    </visual>
  </link>
  
  <joint name="ultrasonic_front_joint" type="fixed">
    <parent link="base_link"/>
    <child link="ultrasonic_front"/>
    <origin xyz="0.15 0.0 0.05" rpy="0.0 0.0 0.0"/>
  </joint>
  
  <!-- Left Ultrasonic -->
  <link name="ultrasonic_left">
    <visual>
      <geometry>
        <box size="0.02 0.02 0.01"/>
      </geometry>
      <material name="green">
        <color rgba="0.0 1.0 0.0 1.0"/>
      </material>
    </visual>
  </link>
  
  <joint name="ultrasonic_left_joint" type="fixed">
    <parent link="base_link"/>
    <child link="ultrasonic_left"/>
    <origin xyz="0.0 0.15 0.05" rpy="0.0 0.0 1.57"/>
  </joint>
  
  <!-- Right Ultrasonic -->
  <link name="ultrasonic_right">
    <visual>
      <geometry>
        <box size="0.02 0.02 0.01"/>
      </geometry>
      <material name="green">
        <color rgba="0.0 1.0 0.0 1.0"/>
      </material>
    </visual>
  </link>
  
  <joint name="ultrasonic_right_joint" type="fixed">
    <parent link="base_link"/>
    <child link="ultrasonic_right"/>
    <origin xyz="0.0 -0.15 0.05" rpy="0.0 0.0 -1.57"/>
  </joint>
  
  <!-- Back Ultrasonic -->
  <link name="ultrasonic_back">
    <visual>
      <geometry>
        <box size="0.02 0.02 0.01"/>
      </geometry>
      <material name="green">
        <color rgba="0.0 1.0 0.0 1.0"/>
      </material>
    </visual>
  </link>
  
  <joint name="ultrasonic_back_joint" type="fixed">
    <parent link="base_link"/>
    <child link="ultrasonic_back"/>
    <origin xyz="-0.15 0.0 0.05" rpy="0.0 0.0 3.14"/>
  </joint>
  
  <!-- Wheels -->
  
  <!-- Left Wheel -->
  <link name="left_wheel">
    <visual>
      <geometry>
        <cylinder radius="0.05" length="0.02"/>
      </geometry>
      <material name="red">
        <color rgba="1.0 0.0 0.0 1.0"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <cylinder radius="0.05" length="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1.0"/>
      <inertia ixx="0.01" ixy="0.0" ixz="0.0" iyy="0.01" iyz="0.0" izz="0.01"/>
    </inertial>
  </link>
  
  <joint name="left_wheel_joint" type="continuous">
    <parent link="base_link"/>
    <child link="left_wheel"/>
    <origin xyz="0.0 0.16 -0.05" rpy="1.57 0.0 0.0"/>
    <axis xyz="0 0 1"/>
  </joint>
  
  <!-- Right Wheel -->
  <link name="right_wheel">
    <visual>
      <geometry>
        <cylinder radius="0.05" length="0.02"/>
      </geometry>
      <material name="red">
        <color rgba="1.0 0.0 0.0 1.0"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <cylinder radius="0.05" length="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1.0"/>
      <inertia ixx="0.01" ixy="0.0" ixz="0.0" iyy="0.01" iyz="0.0" izz="0.01"/>
    </inertial>
  </link>
  
  <joint name="right_wheel_joint" type="continuous">
    <parent link="base_link"/>
    <child link="right_wheel"/>
    <origin xyz="0.0 -0.16 -0.05" rpy="1.57 0.0 0.0"/>
    <axis xyz="0 0 1"/>
  </joint>
  
</robot>
