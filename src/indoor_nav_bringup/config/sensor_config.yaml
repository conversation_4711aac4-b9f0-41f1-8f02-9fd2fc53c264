# Sensor Configuration for Indoor Navigation

lidar_node:
  ros__parameters:
    # Hardware settings
    serial_port: "/dev/ttyUSB0"
    baud_rate: 115200
    
    # Frame settings
    frame_id: "laser_frame"
    
    # Scan parameters
    scan_frequency: 10.0       # Hz
    angle_min: -3.14159        # radians (-180 degrees)
    angle_max: 3.14159         # radians (180 degrees)
    range_min: 0.15            # meters
    range_max: 12.0            # meters
    
    # Simulation settings
    use_simulator: true
    
    # Filtering
    enable_filtering: true
    min_intensity: 10
    
    # Error handling
    max_retries: 3
    retry_delay: 1.0           # seconds

ultrasonic_array:
  ros__parameters:
    # Array configuration
    num_sensors: 6
    
    # Sensor parameters
    max_range: 4.0             # meters
    min_range: 0.02            # meters
    field_of_view: 0.26        # radians (~15 degrees)
    
    # Publishing
    publish_rate: 20.0         # Hz
    
    # Simulation
    use_simulator: true
    
    # Sensor positions (relative to base_link)
    sensor_positions:
      front: [0.15, 0.0, 0.05, 0.0]        # [x, y, z, yaw]
      front_left: [0.12, 0.08, 0.05, 0.52] # 30 degrees
      left: [0.0, 0.15, 0.05, 1.57]        # 90 degrees
      back: [-0.15, 0.0, 0.05, 3.14]       # 180 degrees
      right: [0.0, -0.15, 0.05, -1.57]     # -90 degrees
      front_right: [0.12, -0.08, 0.05, -0.52] # -30 degrees

imu_node:
  ros__parameters:
    # Hardware settings
    serial_port: "/dev/ttyUSB1"
    baud_rate: 115200
    
    # Frame settings
    frame_id: "imu_frame"
    
    # Publishing
    publish_rate: 50.0         # Hz
    
    # Simulation
    use_simulator: true
    
    # Calibration
    gyro_bias: [0.0, 0.0, 0.0]
    accel_bias: [0.0, 0.0, 0.0]
    
    # Noise parameters
    gyro_noise: 0.001
    accel_noise: 0.01

camera_node:
  ros__parameters:
    # Hardware settings
    camera_index: 0
    
    # Image parameters
    image_width: 640
    image_height: 480
    frame_rate: 10.0           # Hz
    
    # Frame settings
    frame_id: "camera_frame"
    
    # Processing
    enable_processing: true
    
    # Simulation
    use_simulator: true
