# Navigation Parameters for Indoor Autonomous Vehicle

navigation_manager:
  ros__parameters:
    # Speed limits
    max_linear_speed: 0.5      # m/s
    max_angular_speed: 1.0     # rad/s
    
    # Goal tolerance
    goal_tolerance: 0.2        # meters
    angular_tolerance: 0.1     # radians
    
    # Obstacle detection
    obstacle_threshold: 0.5    # meters
    emergency_threshold: 0.3   # meters
    
    # Planning
    replanning_timeout: 10.0   # seconds
    path_resolution: 0.1       # meters
    
    # Recovery behavior
    max_recovery_attempts: 3
    recovery_timeout: 5.0      # seconds
    
    # Control gains
    linear_gain: 0.5
    angular_gain: 1.0
    
    # Safety
    enable_emergency_stop: true
    safety_margin: 0.1         # meters

waypoint_manager:
  ros__parameters:
    # File paths
    waypoints_file: "waypoints/saved_waypoints.json"
    backup_file: "waypoints/backup_waypoints.json"
    
    # Auto-save settings
    auto_save: true
    save_interval: 30.0        # seconds
    
    # Position tracking
    position_tolerance: 0.1    # meters
    frequent_location_threshold: 10  # visits
    
    # Cleanup
    max_waypoints: 100
    cleanup_old_waypoints: true
    max_age_days: 365

path_planner:
  ros__parameters:
    # A* algorithm parameters
    heuristic_weight: 1.0
    grid_resolution: 0.05      # meters
    
    # Path smoothing
    enable_smoothing: true
    smoothing_iterations: 10
    
    # Obstacle inflation
    obstacle_inflation: 0.2    # meters
    
    # Planning timeout
    planning_timeout: 5.0      # seconds
