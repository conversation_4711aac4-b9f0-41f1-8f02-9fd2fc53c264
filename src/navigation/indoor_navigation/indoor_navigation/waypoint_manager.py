#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import PoseStamped, PoseWithCovarianceStamped
from std_msgs.msg import String
import json
import os
import time
from datetime import datetime

class WaypointManager(Node):
    def __init__(self):
        super().__init__('waypoint_manager')
        
        # Declare parameters
        self.declare_parameter('waypoints_file', 'waypoints/saved_waypoints.json')
        self.declare_parameter('auto_save', True)
        self.declare_parameter('position_tolerance', 0.1)
        
        # Get parameters
        waypoints_file = self.get_parameter('waypoints_file').get_parameter_value().string_value
        self.auto_save = self.get_parameter('auto_save').get_parameter_value().bool_value
        self.position_tolerance = self.get_parameter('position_tolerance').get_parameter_value().double_value
        
        # File paths
        self.waypoints_file = os.path.expanduser(f'~/.ros/{waypoints_file}')
        self.ensure_directory_exists()
        
        # Subscribers
        self.pose_sub = self.create_subscription(
            PoseWithCovarianceStamped, '/amcl_pose', self.pose_callback, 10)
        self.save_waypoint_sub = self.create_subscription(
            String, '/waypoint/save', self.save_waypoint_callback, 10)
        self.load_waypoints_sub = self.create_subscription(
            String, '/waypoint/load', self.load_waypoints_callback, 10)
        
        # Publishers
        self.waypoint_list_pub = self.create_publisher(String, '/waypoint/list', 10)
        self.waypoint_status_pub = self.create_publisher(String, '/waypoint/status', 10)
        
        # Services (using topics for simplicity)
        self.create_subscription(String, '/waypoint/delete', self.delete_waypoint_callback, 10)
        self.create_subscription(String, '/waypoint/goto', self.goto_waypoint_callback, 10)
        
        # Data storage
        self.current_pose = None
        self.waypoints = {}
        self.frequent_locations = {}
        
        # Load existing waypoints
        self.load_waypoints_from_file()
        
        # Timer for frequent location tracking
        self.location_timer = self.create_timer(5.0, self.track_frequent_locations)
        
        # Timer for publishing waypoint list
        self.publish_timer = self.create_timer(2.0, self.publish_waypoint_list)
        
        self.get_logger().info('Waypoint Manager started')
    
    def ensure_directory_exists(self):
        """Ensure the waypoints directory exists"""
        directory = os.path.dirname(self.waypoints_file)
        if not os.path.exists(directory):
            os.makedirs(directory)
    
    def pose_callback(self, msg):
        """Update current robot pose"""
        self.current_pose = msg.pose.pose
    
    def save_waypoint_callback(self, msg):
        """Save current position as a waypoint"""
        try:
            data = json.loads(msg.data)
            waypoint_name = data.get('name', f'waypoint_{int(time.time())}')
            description = data.get('description', '')
            
            if self.current_pose:
                self.save_waypoint(waypoint_name, self.current_pose, description)
            else:
                self.get_logger().error('No current pose available to save')
                
        except json.JSONDecodeError:
            # Treat as simple string name
            waypoint_name = msg.data.strip()
            if self.current_pose:
                self.save_waypoint(waypoint_name, self.current_pose)
            else:
                self.get_logger().error('No current pose available to save')
    
    def save_waypoint(self, name, pose, description=''):
        """Save a waypoint with given name and pose"""
        waypoint_data = {
            'name': name,
            'x': pose.position.x,
            'y': pose.position.y,
            'z': pose.position.z,
            'orientation': {
                'x': pose.orientation.x,
                'y': pose.orientation.y,
                'z': pose.orientation.z,
                'w': pose.orientation.w
            },
            'description': description,
            'created_time': datetime.now().isoformat(),
            'visit_count': 0
        }
        
        self.waypoints[name] = waypoint_data
        
        if self.auto_save:
            self.save_waypoints_to_file()
        
        self.get_logger().info(f'Waypoint "{name}" saved at ({pose.position.x:.2f}, {pose.position.y:.2f})')
        
        # Publish status
        status = {'action': 'saved', 'waypoint': name, 'success': True}
        self.publish_status(status)
    
    def load_waypoints_callback(self, msg):
        """Load waypoints from file"""
        try:
            data = json.loads(msg.data)
            filename = data.get('filename', self.waypoints_file)
            self.load_waypoints_from_file(filename)
        except json.JSONDecodeError:
            self.load_waypoints_from_file()
    
    def load_waypoints_from_file(self, filename=None):
        """Load waypoints from JSON file"""
        if filename is None:
            filename = self.waypoints_file
        
        try:
            if os.path.exists(filename):
                with open(filename, 'r') as f:
                    data = json.load(f)
                    self.waypoints = data.get('waypoints', {})
                    self.frequent_locations = data.get('frequent_locations', {})
                
                self.get_logger().info(f'Loaded {len(self.waypoints)} waypoints from {filename}')
            else:
                self.get_logger().info('No existing waypoints file found, starting fresh')
                
        except Exception as e:
            self.get_logger().error(f'Failed to load waypoints: {e}')
    
    def save_waypoints_to_file(self, filename=None):
        """Save waypoints to JSON file"""
        if filename is None:
            filename = self.waypoints_file
        
        try:
            data = {
                'waypoints': self.waypoints,
                'frequent_locations': self.frequent_locations,
                'last_updated': datetime.now().isoformat()
            }
            
            with open(filename, 'w') as f:
                json.dump(data, f, indent=2)
            
            self.get_logger().info(f'Saved {len(self.waypoints)} waypoints to {filename}')
            
        except Exception as e:
            self.get_logger().error(f'Failed to save waypoints: {e}')
    
    def delete_waypoint_callback(self, msg):
        """Delete a waypoint"""
        waypoint_name = msg.data.strip()
        
        if waypoint_name in self.waypoints:
            del self.waypoints[waypoint_name]
            
            if self.auto_save:
                self.save_waypoints_to_file()
            
            self.get_logger().info(f'Waypoint "{waypoint_name}" deleted')
            status = {'action': 'deleted', 'waypoint': waypoint_name, 'success': True}
        else:
            self.get_logger().error(f'Waypoint "{waypoint_name}" not found')
            status = {'action': 'deleted', 'waypoint': waypoint_name, 'success': False, 'error': 'not_found'}
        
        self.publish_status(status)
    
    def goto_waypoint_callback(self, msg):
        """Navigate to a specific waypoint"""
        waypoint_name = msg.data.strip()
        
        if waypoint_name in self.waypoints:
            waypoint = self.waypoints[waypoint_name]
            
            # Create goal message
            goal = PoseStamped()
            goal.header.frame_id = 'map'
            goal.header.stamp = self.get_clock().now().to_msg()
            goal.pose.position.x = waypoint['x']
            goal.pose.position.y = waypoint['y']
            goal.pose.position.z = waypoint['z']
            goal.pose.orientation.x = waypoint['orientation']['x']
            goal.pose.orientation.y = waypoint['orientation']['y']
            goal.pose.orientation.z = waypoint['orientation']['z']
            goal.pose.orientation.w = waypoint['orientation']['w']
            
            # Publish goal (navigation manager will pick it up)
            goal_pub = self.create_publisher(PoseStamped, '/move_base_simple/goal', 10)
            goal_pub.publish(goal)
            
            # Update visit count
            waypoint['visit_count'] += 1
            waypoint['last_visited'] = datetime.now().isoformat()
            
            if self.auto_save:
                self.save_waypoints_to_file()
            
            self.get_logger().info(f'Navigating to waypoint "{waypoint_name}"')
            status = {'action': 'goto', 'waypoint': waypoint_name, 'success': True}
        else:
            self.get_logger().error(f'Waypoint "{waypoint_name}" not found')
            status = {'action': 'goto', 'waypoint': waypoint_name, 'success': False, 'error': 'not_found'}
        
        self.publish_status(status)
    
    def track_frequent_locations(self):
        """Track frequently visited locations"""
        if not self.current_pose:
            return
        
        current_x = self.current_pose.position.x
        current_y = self.current_pose.position.y
        
        # Check if robot has been stationary at this location
        location_key = f"{current_x:.1f},{current_y:.1f}"
        
        if location_key in self.frequent_locations:
            self.frequent_locations[location_key]['count'] += 1
            self.frequent_locations[location_key]['last_visit'] = datetime.now().isoformat()
        else:
            self.frequent_locations[location_key] = {
                'x': current_x,
                'y': current_y,
                'count': 1,
                'first_visit': datetime.now().isoformat(),
                'last_visit': datetime.now().isoformat()
            }
        
        # Auto-save frequently visited locations as waypoints
        if self.frequent_locations[location_key]['count'] >= 10:  # Visited 10 times
            auto_name = f"frequent_{location_key.replace(',', '_')}"
            if auto_name not in self.waypoints:
                self.save_waypoint(auto_name, self.current_pose, "Auto-saved frequent location")
    
    def publish_waypoint_list(self):
        """Publish list of available waypoints"""
        waypoint_list = []
        
        for name, data in self.waypoints.items():
            waypoint_info = {
                'name': name,
                'x': data['x'],
                'y': data['y'],
                'description': data.get('description', ''),
                'visit_count': data.get('visit_count', 0),
                'created_time': data.get('created_time', ''),
                'last_visited': data.get('last_visited', '')
            }
            waypoint_list.append(waypoint_info)
        
        # Sort by visit count (most visited first)
        waypoint_list.sort(key=lambda x: x['visit_count'], reverse=True)
        
        list_msg = String()
        list_msg.data = json.dumps({
            'waypoints': waypoint_list,
            'total_count': len(waypoint_list),
            'frequent_locations': len(self.frequent_locations)
        }, indent=2)
        
        self.waypoint_list_pub.publish(list_msg)
    
    def publish_status(self, status_data):
        """Publish waypoint operation status"""
        status_msg = String()
        status_msg.data = json.dumps(status_data)
        self.waypoint_status_pub.publish(status_msg)
    
    def get_nearest_waypoint(self, pose):
        """Find the nearest waypoint to given pose"""
        if not self.waypoints:
            return None
        
        min_distance = float('inf')
        nearest_waypoint = None
        
        for name, waypoint in self.waypoints.items():
            dx = pose.position.x - waypoint['x']
            dy = pose.position.y - waypoint['y']
            distance = (dx*dx + dy*dy)**0.5
            
            if distance < min_distance:
                min_distance = distance
                nearest_waypoint = name
        
        return nearest_waypoint, min_distance
    
    def create_mission_from_waypoints(self, waypoint_names):
        """Create a mission from a list of waypoint names"""
        mission_waypoints = []
        
        for name in waypoint_names:
            if name in self.waypoints:
                waypoint = self.waypoints[name]
                mission_waypoints.append({
                    'name': name,
                    'x': waypoint['x'],
                    'y': waypoint['y'],
                    'description': waypoint.get('description', '')
                })
            else:
                self.get_logger().warn(f'Waypoint "{name}" not found, skipping')
        
        return {
            'mission_id': f'mission_{int(time.time())}',
            'waypoints': mission_waypoints,
            'created_time': datetime.now().isoformat()
        }

def main(args=None):
    rclpy.init(args=args)
    
    try:
        waypoint_manager = WaypointManager()
        rclpy.spin(waypoint_manager)
    except KeyboardInterrupt:
        pass
    finally:
        if 'waypoint_manager' in locals():
            waypoint_manager.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
