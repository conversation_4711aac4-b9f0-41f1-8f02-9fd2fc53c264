#!/bin/bash

echo "=== Checking Dependencies for Indoor Navigation System ==="

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${G<PERSON>EN}[✓]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

# Check ROS2
echo "Checking ROS2 installation..."
if command -v ros2 &> /dev/null; then
    print_status "ROS2 is installed"
    ros2 --version
else
    print_error "ROS2 is not installed or not in PATH"
    echo ""
    echo "Please install ROS2 first:"
    echo "1. Follow instructions at: https://docs.ros.org/en/humble/Installation.html"
    echo "2. Or for Ubuntu: sudo apt install ros-humble-desktop"
    echo "3. Source ROS2: source /opt/ros/humble/setup.bash"
    echo ""
fi

# Check colcon
echo ""
echo "Checking colcon build tool..."
if command -v colcon &> /dev/null; then
    print_status "colcon is installed"
else
    print_error "colcon is not installed"
    echo "Install with: sudo apt install python3-colcon-common-extensions"
fi

# Check Python packages
echo ""
echo "Checking Python dependencies..."

python_packages=("flask" "flask-socketio" "numpy")
for package in "${python_packages[@]}"; do
    if python3 -c "import $package" &> /dev/null; then
        print_status "Python package '$package' is installed"
    else
        print_error "Python package '$package' is missing"
        echo "Install with: pip3 install $package"
    fi
done

# Check if scipy is available (optional)
if python3 -c "import scipy" &> /dev/null; then
    print_status "Python package 'scipy' is installed (optional)"
else
    print_warning "Python package 'scipy' is missing (optional for advanced features)"
    echo "Install with: pip3 install scipy"
fi

echo ""
echo "=== System Information ==="
echo "OS: $(uname -s)"
echo "Architecture: $(uname -m)"
echo "Python version: $(python3 --version)"

echo ""
echo "=== Quick Setup Commands ==="
echo "If you're on Ubuntu and need to install everything:"
echo ""
echo "# Install ROS2 Humble"
echo "sudo apt update"
echo "sudo apt install software-properties-common"
echo "sudo add-apt-repository universe"
echo "sudo apt update && sudo apt install curl -y"
echo "sudo curl -sSL https://raw.githubusercontent.com/ros/rosdistro/master/ros.key -o /usr/share/keyrings/ros-archive-keyring.gpg"
echo "echo \"deb [arch=\$(dpkg --print-architecture) signed-by=/usr/share/keyrings/ros-archive-keyring.gpg] http://packages.ros.org/ros2/ubuntu \$(. /etc/os-release && echo \$UBUNTU_CODENAME) main\" | sudo tee /etc/apt/sources.list.d/ros2.list > /dev/null"
echo "sudo apt update"
echo "sudo apt install ros-humble-desktop"
echo ""
echo "# Install build tools"
echo "sudo apt install python3-colcon-common-extensions"
echo ""
echo "# Install Python packages"
echo "pip3 install flask flask-socketio numpy scipy"
echo ""
echo "# Source ROS2 (add to ~/.bashrc for permanent)"
echo "source /opt/ros/humble/setup.bash"
echo ""
echo "Then run: ./setup_indoor_navigation.sh"
