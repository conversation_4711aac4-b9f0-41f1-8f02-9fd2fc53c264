#!/bin/bash

echo "🤖 Indoor Navigation System - Structure Validation"
echo "=================================================="

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_check() {
    if [ -e "$1" ]; then
        echo -e "${GREEN}✅${NC} $1"
        return 0
    else
        echo -e "${RED}❌${NC} $1"
        return 1
    fi
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

echo ""
echo "📁 Checking package structure:"

# Check main directories
print_check "src/"
print_check "launch/"
print_check "config/"

echo ""
echo "📦 Checking packages:"

# Check bringup package
print_check "src/indoor_nav_bringup/"
print_check "src/indoor_nav_bringup/package.xml"
print_check "src/indoor_nav_bringup/CMakeLists.txt"
print_check "src/indoor_nav_bringup/launch/"

# Check sensor packages
print_check "src/sensors/lidar_node/"
print_check "src/sensors/lidar_node/package.xml"
print_check "src/sensors/lidar_node/setup.py"

print_check "src/sensors/ultrasonic_array/"
print_check "src/sensors/ultrasonic_array/package.xml"
print_check "src/sensors/ultrasonic_array/setup.py"

# Check navigation package
print_check "src/navigation/indoor_navigation/"
print_check "src/navigation/indoor_navigation/package.xml"
print_check "src/navigation/indoor_navigation/setup.py"

# Check interface package
print_check "src/interfaces/web_interface/"
print_check "src/interfaces/web_interface/package.xml"
print_check "src/interfaces/web_interface/setup.py"

echo ""
echo "🚀 Checking launch files:"
print_check "src/indoor_nav_bringup/launch/indoor_navigation_system.launch.py"

echo ""
echo "⚙️ Checking config files:"
print_check "src/indoor_nav_bringup/config/navigation_params.yaml"
print_check "src/indoor_nav_bringup/config/sensor_config.yaml"

echo ""
echo "📄 Checking documentation:"
print_check "README.md"
print_check "setup_indoor_navigation.sh"

echo ""
echo "🔍 Launch file syntax check:"
if [ -f "src/indoor_nav_bringup/launch/indoor_navigation_system.launch.py" ]; then
    python3 -m py_compile src/indoor_nav_bringup/launch/indoor_navigation_system.launch.py 2>/dev/null
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅${NC} Launch file syntax is valid"
    else
        echo -e "${RED}❌${NC} Launch file has syntax errors"
    fi
else
    echo -e "${RED}❌${NC} Launch file not found"
fi

echo ""
echo "📋 Summary:"
echo "The project structure is set up for an indoor navigation system with:"
echo "  🔍 LiDAR sensor for mapping and obstacle detection"
echo "  📡 Ultrasonic array for proximity sensing"
echo "  🗺️ Navigation manager for path planning"
echo "  📍 Waypoint manager for location management"
echo "  🌐 Web interface for user control"

echo ""
echo "🚀 Next steps:"
echo "1. Install ROS2 if not already installed"
echo "2. Run: ./setup_indoor_navigation.sh"
echo "3. Launch: ros2 launch indoor_nav_bringup indoor_navigation_system.launch.py"

echo ""
echo "💡 If you encounter the error 'is not a valid package name':"
echo "   Make sure you're using the correct command:"
echo "   ros2 launch indoor_nav_bringup indoor_navigation_system.launch.py"
echo "   (Note: 'indoor_nav_bringup' is the package name)"
