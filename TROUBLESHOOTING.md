# 🔧 Troubleshooting Guide - Indoor Navigation System

## ❌ Error: "is not a valid package name"

### Problem
<PERSON>hi chạy `ros2 launch indoor_navigation_system.launch.py` bạn gặp lỗi:
```
ValueError: 'indoor_navigation_system.launch.py' is not a valid package name
```

### Solution
Lỗi này xảy ra vì bạn đang cố gắng launch file trực tiếp thay vì thông qua package. 

**❌ Sai:**
```bash
ros2 launch indoor_navigation_system.launch.py
```

**✅ Đúng:**
```bash
ros2 launch indoor_nav_bringup indoor_navigation_system.launch.py
```

### Explanation
- `indoor_nav_bringup` là tên package
- `indoor_navigation_system.launch.py` là tên file launch trong package đó

## 🚀 Complete Setup Process

### 1. Check Dependencies
```bash
chmod +x check_dependencies.sh
./check_dependencies.sh
```

### 2. Validate Structure
```bash
chmod +x validate_structure.sh
./validate_structure.sh
```

### 3. Build System
```bash
chmod +x setup_indoor_navigation.sh
./setup_indoor_navigation.sh
```

### 4. Launch System
```bash
# Source ROS2 first
source /opt/ros/humble/setup.bash

# Source workspace
source install/setup.bash

# Launch the system
ros2 launch indoor_nav_bringup indoor_navigation_system.launch.py
```

## 🐛 Common Issues

### Issue 1: ROS2 Not Found
**Error:** `ros2: command not found`

**Solution:**
```bash
# Install ROS2 Humble
sudo apt update
sudo apt install ros-humble-desktop

# Source ROS2
source /opt/ros/humble/setup.bash

# Add to bashrc for permanent
echo "source /opt/ros/humble/setup.bash" >> ~/.bashrc
```

### Issue 2: colcon Not Found
**Error:** `colcon: command not found`

**Solution:**
```bash
sudo apt install python3-colcon-common-extensions
```

### Issue 3: Python Dependencies Missing
**Error:** `ModuleNotFoundError: No module named 'flask'`

**Solution:**
```bash
pip3 install flask flask-socketio numpy scipy
```

### Issue 4: Package Not Built
**Error:** `Package 'lidar_node' not found`

**Solution:**
```bash
# Build all packages
colcon build

# Or build specific package
colcon build --packages-select lidar_node
```

### Issue 5: Permission Denied
**Error:** `Permission denied` when accessing serial ports

**Solution:**
```bash
# Add user to dialout group
sudo usermod -a -G dialout $USER

# Logout and login again, or:
newgrp dialout
```

## 🧪 Testing Individual Components

### Test Launch File Syntax
```bash
python3 -m py_compile src/indoor_nav_bringup/launch/indoor_navigation_system.launch.py
```

### Test Individual Packages
```bash
# Test LiDAR simulator
ros2 run lidar_node lidar_node --ros-args -p use_simulator:=true

# Test ultrasonic array
ros2 run ultrasonic_array ultrasonic_array_node

# Test navigation manager
ros2 run indoor_navigation navigation_manager

# Test web interface
ros2 run web_interface web_server
```

### Test Topics
```bash
# List all topics
ros2 topic list

# Check LiDAR data
ros2 topic echo /scan

# Check ultrasonic data
ros2 topic echo /ultrasonic/proximity

# Check navigation status
ros2 topic echo /navigation/status
```

## 🌐 Web Interface Issues

### Issue: Web Interface Not Accessible
**Problem:** Cannot access http://localhost:8080

**Solutions:**
1. Check if web server is running:
   ```bash
   ros2 node list | grep web_server
   ```

2. Check port availability:
   ```bash
   netstat -tulpn | grep 8080
   ```

3. Try different port:
   ```bash
   ros2 launch indoor_nav_bringup indoor_navigation_system.launch.py web_port:=9090
   ```

4. Check firewall:
   ```bash
   sudo ufw allow 8080
   ```

### Issue: Flask Dependencies
**Error:** `ImportError: No module named 'flask'`

**Solution:**
```bash
pip3 install flask flask-socketio
```

## 🔧 Hardware-Specific Issues

### LiDAR Issues
1. **Device not found:** Check USB connection and permissions
2. **Wrong baud rate:** Verify LiDAR specifications
3. **Driver issues:** Install appropriate drivers

### Ultrasonic Sensor Issues
1. **GPIO permissions:** Run with sudo or fix permissions
2. **Wiring problems:** Check connections
3. **Interference:** Ensure sensors don't interfere with each other

## 📊 Performance Optimization

### Reduce CPU Usage
```bash
# Lower sensor frequencies
ros2 param set /lidar_node scan_frequency 5.0
ros2 param set /ultrasonic_array_node publish_rate 10.0
```

### Memory Optimization
```bash
# Limit history size
ros2 param set /navigation_manager max_history_length 50
```

## 🆘 Getting Help

### Check System Status
```bash
# Node status
ros2 node list

# Topic status
ros2 topic list

# Parameter status
ros2 param list

# Service status
ros2 service list
```

### Debug Logs
```bash
# View logs
ros2 log view

# Specific node logs
ros2 log view navigation_manager
```

### System Information
```bash
# ROS2 version
ros2 --version

# System info
uname -a
python3 --version
```

## 📝 Quick Reference

### Essential Commands
```bash
# Build workspace
colcon build

# Source workspace
source install/setup.bash

# Launch system
ros2 launch indoor_nav_bringup indoor_navigation_system.launch.py

# Emergency stop
ros2 topic pub /cmd_vel geometry_msgs/Twist '{linear: {x: 0}, angular: {z: 0}}'

# Save waypoint
ros2 topic pub /waypoint/save std_msgs/String 'data: "kitchen"'

# Go to waypoint
ros2 topic pub /waypoint/goto std_msgs/String 'data: "kitchen"'
```

### Useful Launch Options
```bash
# Simulator mode
ros2 launch indoor_nav_bringup indoor_navigation_system.launch.py use_simulator:=true

# Hardware mode
ros2 launch indoor_nav_bringup indoor_navigation_system.launch.py use_simulator:=false

# Without web interface
ros2 launch indoor_nav_bringup indoor_navigation_system.launch.py use_web_interface:=false

# LiDAR only
ros2 launch indoor_nav_bringup indoor_navigation_system.launch.py use_ultrasonic:=false
```
